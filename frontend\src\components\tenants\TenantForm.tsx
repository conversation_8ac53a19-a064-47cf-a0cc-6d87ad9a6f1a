import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuthStore } from '../../stores/authStore';
import { useToastStore } from '../../stores/toastStore';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { XMarkIcon } from '@heroicons/react/24/outline';

const tenantSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  displayName: z.string().min(1, 'Display name is required').max(255),
  domain: z.string().optional(),
  description: z.string().optional(),
  settings: z.object({
    maxUsers: z.number().min(1).max(1000).default(10),
    maxStorage: z.number().min(1).max(1000).default(100), // GB
    allowFtpSources: z.boolean().default(true),
    allowTimelapses: z.boolean().default(true),
    retentionDays: z.number().min(1).max(365).default(90),
  }).default({}),
  isActive: z.boolean().default(true),
});

type TenantFormData = z.infer<typeof tenantSchema>;

interface Tenant {
  id: string;
  name: string;
  display_name: string;
  domain?: string;
  description?: string;
  settings: any;
  is_active: boolean;
  created_at: string;
  user_count?: number;
  storage_used?: number;
}

interface TenantFormProps {
  tenant?: Tenant;
  onClose: () => void;
  onSave: (tenant: Tenant) => void;
}

export function TenantForm({ tenant, onClose, onSave }: TenantFormProps) {
  const { token } = useAuthStore();
  const { success, error } = useToastStore();
  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<TenantFormData>({
    resolver: zodResolver(tenantSchema),
    defaultValues: tenant ? {
      name: tenant.name,
      displayName: tenant.display_name,
      domain: tenant.domain || '',
      description: tenant.description || '',
      settings: {
        maxUsers: tenant.settings?.maxUsers || 10,
        maxStorage: tenant.settings?.maxStorage || 100,
        allowFtpSources: tenant.settings?.allowFtpSources !== false,
        allowTimelapses: tenant.settings?.allowTimelapses !== false,
        retentionDays: tenant.settings?.retentionDays || 90,
      },
      isActive: tenant.is_active,
    } : {
      name: '',
      displayName: '',
      domain: '',
      description: '',
      settings: {
        maxUsers: 10,
        maxStorage: 100,
        allowFtpSources: true,
        allowTimelapses: true,
        retentionDays: 90,
      },
      isActive: true,
    }
  });

  const onSubmit = async (data: TenantFormData) => {
    setLoading(true);

    try {
      const url = tenant ? `/api/tenants/${tenant.id}` : '/api/tenants';
      const method = tenant ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        success(
          tenant ? 'Tenant Updated' : 'Tenant Created',
          `Successfully ${tenant ? 'updated' : 'created'} tenant "${data.displayName}"`
        );
        onSave(result.tenant);
        onClose();
      } else {
        const errorData = await response.json();
        error('Failed to Save', errorData.error);
      }
    } catch (err) {
      console.error('Failed to save tenant:', err);
      error('Connection Error', 'Failed to save tenant. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const generateSlug = (displayName: string) => {
    return displayName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  const handleDisplayNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const displayName = e.target.value;
    setValue('displayName', displayName);
    
    // Auto-generate name slug if creating new tenant
    if (!tenant) {
      setValue('name', generateSlug(displayName));
    }
  };

  const storagePresets = [
    { label: '10 GB', value: 10 },
    { label: '50 GB', value: 50 },
    { label: '100 GB', value: 100 },
    { label: '500 GB', value: 500 },
    { label: '1 TB', value: 1000 },
  ];

  const userPresets = [
    { label: '5 users', value: 5 },
    { label: '10 users', value: 10 },
    { label: '25 users', value: 25 },
    { label: '50 users', value: 50 },
    { label: '100 users', value: 100 },
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-3xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900">
            {tenant ? 'Edit Tenant' : 'Add Tenant'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={loading}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="displayName" className="block text-sm font-medium text-gray-700">
                Display Name *
              </label>
              <input
                {...register('displayName')}
                onChange={handleDisplayNameChange}
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Acme Corporation"
              />
              {errors.displayName && (
                <p className="mt-1 text-sm text-red-600">{errors.displayName.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Slug *
              </label>
              <input
                {...register('name')}
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="acme-corporation"
                readOnly={!!tenant}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                {tenant ? 'Slug cannot be changed after creation' : 'Auto-generated from display name'}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="domain" className="block text-sm font-medium text-gray-700">
                Domain
              </label>
              <input
                {...register('domain')}
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="acme.com"
              />
              <p className="mt-1 text-xs text-gray-500">
                Optional domain for email validation
              </p>
            </div>

            <div className="flex items-center space-x-4 pt-6">
              <label className="flex items-center">
                <input
                  {...register('isActive')}
                  type="checkbox"
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">
                  Active tenant
                </span>
              </label>
            </div>
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description
            </label>
            <textarea
              {...register('description')}
              rows={3}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Brief description of the organization..."
            />
          </div>

          {/* Settings */}
          <div className="border-t pt-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Tenant Settings</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* User Limits */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Users
                </label>
                <div className="space-y-2">
                  <div className="flex space-x-2">
                    {userPresets.map((preset) => (
                      <button
                        key={preset.value}
                        type="button"
                        onClick={() => setValue('settings.maxUsers', preset.value)}
                        className={`px-3 py-1 text-xs rounded ${
                          watch('settings.maxUsers') === preset.value
                            ? 'bg-primary-600 text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        {preset.label}
                      </button>
                    ))}
                  </div>
                  <input
                    {...register('settings.maxUsers', { valueAsNumber: true })}
                    type="number"
                    min="1"
                    max="1000"
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Storage Limits */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Storage Limit (GB)
                </label>
                <div className="space-y-2">
                  <div className="flex space-x-2">
                    {storagePresets.map((preset) => (
                      <button
                        key={preset.value}
                        type="button"
                        onClick={() => setValue('settings.maxStorage', preset.value)}
                        className={`px-3 py-1 text-xs rounded ${
                          watch('settings.maxStorage') === preset.value
                            ? 'bg-primary-600 text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        {preset.label}
                      </button>
                    ))}
                  </div>
                  <input
                    {...register('settings.maxStorage', { valueAsNumber: true })}
                    type="number"
                    min="1"
                    max="10000"
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Data Retention (days)
              </label>
              <input
                {...register('settings.retentionDays', { valueAsNumber: true })}
                type="number"
                min="1"
                max="365"
                className="block w-32 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <p className="mt-1 text-xs text-gray-500">
                How long to keep assets before automatic deletion
              </p>
            </div>

            {/* Feature Toggles */}
            <div className="mt-6">
              <h5 className="text-sm font-medium text-gray-700 mb-3">Features</h5>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    {...register('settings.allowFtpSources')}
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Allow FTP Sources
                  </span>
                </label>
                
                <label className="flex items-center">
                  <input
                    {...register('settings.allowTimelapses')}
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Allow Timelapse Creation
                  </span>
                </label>
              </div>
            </div>
          </div>

          {/* Preview */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Tenant Preview</h4>
            <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
              <div>
                <dt className="text-blue-700">Display Name:</dt>
                <dd className="text-blue-900 font-medium">{watch('displayName') || 'Not set'}</dd>
              </div>
              <div>
                <dt className="text-blue-700">Slug:</dt>
                <dd className="text-blue-900 font-medium">{watch('name') || 'Not set'}</dd>
              </div>
              <div>
                <dt className="text-blue-700">Max Users:</dt>
                <dd className="text-blue-900 font-medium">{watch('settings.maxUsers')}</dd>
              </div>
              <div>
                <dt className="text-blue-700">Storage Limit:</dt>
                <dd className="text-blue-900 font-medium">{watch('settings.maxStorage')} GB</dd>
              </div>
            </dl>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading && <LoadingSpinner size="sm" className="mr-2" />}
              {tenant ? 'Update' : 'Create'} Tenant
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
