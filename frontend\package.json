{"name": "@eagleview/frontend", "version": "1.0.0", "description": "EagleView Frontend - React SPA", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "clean": "rm -rf dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "@auth0/auth0-react": "^2.2.4", "zustand": "^4.4.7", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "video.js": "^8.6.1", "@videojs/themes": "^1.0.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/video.js": "^7.3.55", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}