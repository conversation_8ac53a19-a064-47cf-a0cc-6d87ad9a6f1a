# EagleView MVP - Local Setup Script for Windows (Without Docker)
# This script helps set up the development environment locally

Write-Host "Setting up EagleView MVP Development Environment (Local)" -ForegroundColor Green
Write-Host "=========================================================" -ForegroundColor Green

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check Node.js
Write-Host "Checking Node.js..." -ForegroundColor Yellow
if (Test-Command "node") {
    $nodeVersion = node --version
    Write-Host "Node.js $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "Node.js is not installed. Please install Node.js 18+ first." -ForegroundColor Red
    exit 1
}

# Check PNPM
Write-Host "Checking PNPM..." -ForegroundColor Yellow
if (Test-Command "pnpm") {
    $pnpmVersion = pnpm --version
    Write-Host "PNPM $pnpmVersion" -ForegroundColor Green
} else {
    Write-Host "Installing PNPM..." -ForegroundColor Yellow
    npm install -g pnpm
}

# Check PostgreSQL
Write-Host "📋 Checking PostgreSQL..." -ForegroundColor Yellow
if (Test-Command "psql") {
    Write-Host "✅ PostgreSQL is installed" -ForegroundColor Green
} else {
    Write-Host "❌ PostgreSQL is not installed." -ForegroundColor Red
    Write-Host "Please install PostgreSQL:" -ForegroundColor Yellow
    Write-Host "  - Via Chocolatey: choco install postgresql" -ForegroundColor Cyan
    Write-Host "  - Or download from: https://www.postgresql.org/download/windows/" -ForegroundColor Cyan
    $continue = Read-Host "Continue without PostgreSQL? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# Check Redis
Write-Host "📋 Checking Redis..." -ForegroundColor Yellow
if (Test-Command "redis-server") {
    Write-Host "✅ Redis is installed" -ForegroundColor Green
} else {
    Write-Host "❌ Redis is not installed." -ForegroundColor Red
    Write-Host "Please install Redis:" -ForegroundColor Yellow
    Write-Host "  - Via Chocolatey: choco install redis-64" -ForegroundColor Cyan
    Write-Host "  - Or use WSL with Redis" -ForegroundColor Cyan
    Write-Host "  - Or download Redis Stack from: https://redis.io/download" -ForegroundColor Cyan
    $continue = Read-Host "Continue without Redis? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# Check MinIO
Write-Host "📋 Checking MinIO..." -ForegroundColor Yellow
if (Test-Command "minio") {
    Write-Host "✅ MinIO is installed" -ForegroundColor Green
} else {
    Write-Host "❌ MinIO is not installed." -ForegroundColor Red
    Write-Host "Please install MinIO:" -ForegroundColor Yellow
    Write-Host "  - Via Chocolatey: choco install minio" -ForegroundColor Cyan
    Write-Host "  - Or download from: https://min.io/download#/windows" -ForegroundColor Cyan
    $continue = Read-Host "Continue without MinIO? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# Check FFmpeg
Write-Host "📋 Checking FFmpeg..." -ForegroundColor Yellow
if (Test-Command "ffmpeg") {
    Write-Host "✅ FFmpeg is installed" -ForegroundColor Green
} else {
    Write-Host "⚠️  FFmpeg is not installed. Video processing will not work." -ForegroundColor Yellow
    Write-Host "Please install FFmpeg:" -ForegroundColor Yellow
    Write-Host "  - Via Chocolatey: choco install ffmpeg" -ForegroundColor Cyan
    Write-Host "  - Or download from: https://ffmpeg.org/download.html" -ForegroundColor Cyan
}

# Install dependencies
Write-Host ""
Write-Host "📦 Installing project dependencies..." -ForegroundColor Yellow
pnpm install
Write-Host "✅ Dependencies installed" -ForegroundColor Green

# Setup environment files
Write-Host ""
Write-Host "⚙️  Setting up environment files..." -ForegroundColor Yellow

if (!(Test-Path "api\.env")) {
    Copy-Item "api\.env.example" "api\.env"
    Write-Host "✅ Created api\.env" -ForegroundColor Green
    Write-Host "⚠️  Please update api\.env with your Auth0 credentials" -ForegroundColor Yellow
} else {
    Write-Host "✅ api\.env already exists" -ForegroundColor Green
}

if (!(Test-Path "frontend\.env")) {
    Copy-Item "frontend\.env.example" "frontend\.env"
    Write-Host "✅ Created frontend\.env" -ForegroundColor Green
    Write-Host "⚠️  Please update frontend\.env with your Auth0 credentials" -ForegroundColor Yellow
} else {
    Write-Host "✅ frontend\.env already exists" -ForegroundColor Green
}

# Create logs directory
Write-Host ""
Write-Host "📝 Creating logs directory..." -ForegroundColor Yellow
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs"
}
Write-Host "✅ Logs directory created" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 Setup completed!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
