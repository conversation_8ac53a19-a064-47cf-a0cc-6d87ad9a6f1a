import { useState } from 'react';
import { CheckIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { LoadingSpinner } from '../ui/LoadingSpinner';

interface Asset {
  id: string;
  name: string;
  type: string;
  file_path: string;
  file_size: number;
  metadata: any;
  created_at: string;
}

interface AssetGridProps {
  assets: Asset[];
  selectedAssets: Asset[];
  onAssetSelect: (asset: Asset) => void;
}

export function AssetGrid({ assets, selectedAssets, onAssetSelect }: AssetGridProps) {
  const [filter, setFilter] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'size'>('date');
  const [searchTerm, setSearchTerm] = useState('');

  const isSelected = (asset: Asset) => {
    return selectedAssets.some(a => a.id === asset.id);
  };

  const getSelectedIndex = (asset: Asset) => {
    return selectedAssets.findIndex(a => a.id === asset.id) + 1;
  };

  const filterAssets = (assets: Asset[]) => {
    let filtered = assets;

    // Filter by time period
    if (filter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (filter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
      }
      
      filtered = filtered.filter(asset => 
        new Date(asset.created_at) >= filterDate
      );
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(asset =>
        asset.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort assets
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'name':
          return a.name.localeCompare(b.name);
        case 'size':
          return b.file_size - a.file_size;
        default:
          return 0;
      }
    });

    return filtered;
  };

  const filteredAssets = filterAssets(assets);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="space-y-4">
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search images..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        
        <div className="flex gap-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="date">Sort by Date</option>
            <option value="name">Sort by Name</option>
            <option value="size">Sort by Size</option>
          </select>
        </div>
      </div>

      {/* Asset Count */}
      <div className="text-sm text-gray-600">
        {filteredAssets.length} image{filteredAssets.length !== 1 ? 's' : ''} available
        {selectedAssets.length > 0 && (
          <span className="ml-2 text-primary-600 font-medium">
            ({selectedAssets.length} selected)
          </span>
        )}
      </div>

      {/* Asset Grid */}
      {filteredAssets.length === 0 ? (
        <div className="text-center py-12">
          <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">No images found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || filter !== 'all' 
              ? 'Try adjusting your filters or search term.'
              : 'Upload some images to get started.'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {filteredAssets.map((asset) => (
            <div
              key={asset.id}
              onClick={() => onAssetSelect(asset)}
              className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all hover:shadow-md ${
                isSelected(asset)
                  ? 'border-primary-500 ring-2 ring-primary-200'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              {/* Image placeholder */}
              <div className="aspect-square bg-gray-100 flex items-center justify-center">
                <PhotoIcon className="h-8 w-8 text-gray-400" />
              </div>
              
              {/* Selection indicator */}
              {isSelected(asset) && (
                <div className="absolute top-2 right-2 w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">
                    {getSelectedIndex(asset)}
                  </span>
                </div>
              )}
              
              {/* Asset info */}
              <div className="p-2 bg-white">
                <p className="text-xs font-medium text-gray-900 truncate">
                  {asset.name}
                </p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(asset.file_size)}
                </p>
                <p className="text-xs text-gray-400">
                  {formatDate(asset.created_at)}
                </p>
              </div>
              
              {/* Hover overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all flex items-center justify-center">
                {isSelected(asset) && (
                  <div className="bg-primary-600 rounded-full p-1">
                    <CheckIcon className="h-4 w-4 text-white" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Selection Actions */}
      {selectedAssets.length > 0 && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg border p-4 z-10">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-900">
              {selectedAssets.length} frame{selectedAssets.length !== 1 ? 's' : ''} selected
            </span>
            <button
              onClick={() => {
                // Select all visible assets
                filteredAssets.forEach(asset => {
                  if (!isSelected(asset)) {
                    onAssetSelect(asset);
                  }
                });
              }}
              className="text-sm text-primary-600 hover:text-primary-700 font-medium"
            >
              Select All ({filteredAssets.length})
            </button>
            <button
              onClick={() => {
                // Deselect all
                selectedAssets.forEach(asset => {
                  onAssetSelect(asset);
                });
              }}
              className="text-sm text-gray-600 hover:text-gray-700 font-medium"
            >
              Clear Selection
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
