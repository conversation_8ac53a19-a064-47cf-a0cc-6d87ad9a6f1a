import { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon,
  PlayIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface Job {
  id: string;
  type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  error_message?: string;
  result?: any;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  created_by_name?: string;
}

interface JobProgressProps {
  jobId?: string;
  showAll?: boolean;
  onJobComplete?: (job: Job) => void;
}

export function JobProgress({ jobId, showAll = false, onJobComplete }: JobProgressProps) {
  const { token, user } = useAuthStore();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchJobs();
    
    // Set up polling for active jobs
    const interval = setInterval(() => {
      const hasActiveJobs = jobs.some(job => 
        job.status === 'pending' || job.status === 'processing'
      );
      
      if (hasActiveJobs) {
        fetchJobs();
      }
    }, 2000); // Poll every 2 seconds

    return () => clearInterval(interval);
  }, [jobId, showAll]);

  const fetchJobs = async () => {
    try {
      let url = '/api/jobs';
      const params = new URLSearchParams();
      
      if (jobId) {
        url = `/api/jobs/${jobId}`;
      } else if (!showAll) {
        params.append('limit', '5');
      }

      const response = await fetch(`${url}${params.toString() ? '?' + params.toString() : ''}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        
        if (jobId) {
          setJobs([data.job]);
        } else {
          setJobs(data.jobs);
        }

        // Check for newly completed jobs
        if (onJobComplete) {
          const completedJobs = (jobId ? [data.job] : data.jobs).filter(
            (job: Job) => job.status === 'completed'
          );
          
          completedJobs.forEach((job: Job) => {
            onJobComplete(job);
          });
        }
      }
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  const retryJob = async (jobId: string) => {
    try {
      const response = await fetch(`/api/jobs/${jobId}/retry`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        fetchJobs(); // Refresh jobs
      }
    } catch (error) {
      console.error('Failed to retry job:', error);
    }
  };

  const cancelJob = async (jobId: string) => {
    try {
      const response = await fetch(`/api/jobs/${jobId}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        fetchJobs(); // Refresh jobs
      }
    } catch (error) {
      console.error('Failed to cancel job:', error);
    }
  };

  const getStatusIcon = (job: Job) => {
    switch (job.status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
      case 'processing':
        return <LoadingSpinner size="sm" />;
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-gray-600 bg-gray-100';
      case 'processing': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getJobTypeIcon = (type: string) => {
    switch (type) {
      case 'timelapse':
        return <PlayIcon className="h-4 w-4" />;
      case 'thumbnail':
        return <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
        </svg>;
      case 'ftp-sync':
        return <ArrowPathIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const formatDuration = (startTime?: string, endTime?: string) => {
    if (!startTime) return null;
    
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.round((end.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) {
      return `${duration}s`;
    } else if (duration < 3600) {
      return `${Math.floor(duration / 60)}m ${duration % 60}s`;
    } else {
      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-4">
        <LoadingSpinner size="md" />
      </div>
    );
  }

  if (jobs.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
        <p className="mt-2 text-sm">No jobs found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {jobs.map((job) => (
        <div
          key={job.id}
          className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getStatusIcon(job)}
              <div>
                <div className="flex items-center space-x-2">
                  {getJobTypeIcon(job.type)}
                  <h4 className="text-sm font-medium text-gray-900 capitalize">
                    {job.type.replace('-', ' ')} Job
                  </h4>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(job.status)}`}>
                    {job.status}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Created {new Date(job.created_at).toLocaleString()}
                  {job.created_by_name && ` by ${job.created_by_name}`}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {job.status === 'failed' && user?.role === 'admin' && (
                <button
                  onClick={() => retryJob(job.id)}
                  className="text-xs text-blue-600 hover:text-blue-700 font-medium"
                >
                  Retry
                </button>
              )}
              
              {(job.status === 'pending' || job.status === 'processing') && user?.role === 'admin' && (
                <button
                  onClick={() => cancelJob(job.id)}
                  className="text-xs text-red-600 hover:text-red-700 font-medium"
                >
                  Cancel
                </button>
              )}
            </div>
          </div>

          {/* Progress Bar */}
          {job.status === 'processing' && (
            <div className="mt-3">
              <div className="flex justify-between text-xs text-gray-600 mb-1">
                <span>Progress</span>
                <span>{job.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${job.progress}%` }}
                />
              </div>
            </div>
          )}

          {/* Duration */}
          {job.started_at && (
            <div className="mt-2 text-xs text-gray-500">
              Duration: {formatDuration(job.started_at, job.completed_at)}
            </div>
          )}

          {/* Error Message */}
          {job.status === 'failed' && job.error_message && (
            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
              {job.error_message}
            </div>
          )}

          {/* Result */}
          {job.status === 'completed' && job.result && (
            <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
              {job.type === 'timelapse' && job.result.frameCount && (
                <div>
                  ✅ Timelapse created with {job.result.frameCount} frames 
                  ({job.result.duration?.toFixed(1)}s duration)
                </div>
              )}
              {job.type === 'thumbnail' && job.result.thumbnails && (
                <div>
                  ✅ Generated {job.result.thumbnails.length} thumbnail sizes
                </div>
              )}
              {job.type === 'ftp-sync' && job.result.filesProcessed !== undefined && (
                <div>
                  ✅ Processed {job.result.filesProcessed} files from FTP
                </div>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
