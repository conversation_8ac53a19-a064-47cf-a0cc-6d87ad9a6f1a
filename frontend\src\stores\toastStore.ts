import { create } from 'zustand';
import { Toast, ToastType } from '../components/ui/Toast';

interface ToastState {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => void;
  dismissToast: (id: string) => void;
  success: (title: string, message?: string, options?: Partial<Toast>) => void;
  error: (title: string, message?: string, options?: Partial<Toast>) => void;
  warning: (title: string, message?: string, options?: Partial<Toast>) => void;
  info: (title: string, message?: string, options?: Partial<Toast>) => void;
  clear: () => void;
}

export const useToastStore = create<ToastState>((set, get) => ({
  toasts: [],
  
  addToast: (toast) => {
    const id = Math.random().toString(36).substring(7);
    const newToast = { ...toast, id };
    
    set((state) => ({
      toasts: [...state.toasts, newToast]
    }));
    
    // Auto dismiss if duration is set
    if (toast.duration !== 0) {
      setTimeout(() => {
        get().dismissToast(id);
      }, toast.duration || 5000);
    }
  },
  
  dismissToast: (id) => {
    set((state) => ({
      toasts: state.toasts.filter(toast => toast.id !== id)
    }));
  },
  
  success: (title, message, options) => {
    get().addToast({ 
      type: 'success', 
      title, 
      message, 
      duration: 4000,
      ...options 
    });
  },
  
  error: (title, message, options) => {
    get().addToast({ 
      type: 'error', 
      title, 
      message, 
      duration: 6000,
      ...options 
    });
  },
  
  warning: (title, message, options) => {
    get().addToast({ 
      type: 'warning', 
      title, 
      message, 
      duration: 5000,
      ...options 
    });
  },
  
  info: (title, message, options) => {
    get().addToast({ 
      type: 'info', 
      title, 
      message, 
      duration: 4000,
      ...options 
    });
  },
  
  clear: () => {
    set({ toasts: [] });
  }
}));
