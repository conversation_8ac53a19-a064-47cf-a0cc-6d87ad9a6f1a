A plataforma que você descreveu exige um SaaS multi-tenant com controle ﬁno de permissões, um pipeline de vídeo assíncrono e uma UI de edição/preview altamente responsiva. A seguir apresento um blueprint completo — do modelo de dados ao roadmap de MVP — ancorado em bibliotecas e padrões de mercado já testados.

## 1. Fluxo de Usuários & Permissões

**Tipos de usuário**

* **Administradores (staff Eagle View):** podem criar grupos (empresas-cliente), importar imagens via FTP, selecionar quadros, gerar timelapses, publicar e revogar acesso.
* **Clientes:** pertencem exatamente a um grupo (empresa). Só enxergam os assets que o administrador marcou como “publicado” para seu grupo.

**Autorização**

* Use RBAC/ABAC nativo da Auth0 Organizations ou Okta FGA, que já tratam multi-tenant e delegação granular de escopos (`read:asset`, `edit:asset`, etc.) ([auth0.com][1]).
* Cada “grupo-empresa” vira uma *Organization*; dentro dela, usuários têm funções Client ou Admin-Viewer.

## 2. Arquitetura de Alto Nível

| Camada                   | Tecnologias sugeridas                                                                                                                                                             | Função                                 |
| ------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------- |
| **Frontend SPA**         | React + Vite, Zustand para estado, Tailwind; Video.js p/ player 4K ([videojs.com][2]); ffmpeg.wasm p/ pré-visualização rápida no navegador ([github.com][3], [cloudinary.com][4]) | UI, upload de seleções, pré-preview    |
| **API Gateway**          | Node.js + Express (ou Fastify)                                                                                                                                                    | Autenticação JWT, rotas REST           |
| **Job Worker**           | BullMQ + Redis para fila ([bullmq.io][5], [docs.bullmq.io][6])                                                                                                                    | Render de timelapse, thumbs, transcode |
| **Processador de Vídeo** | FFmpeg via fluent-ffmpeg ([npmjs.com][7])                                                                                                                                         | Concatenação & encoding                |
| **Storage**              | S3 (ou MinIO) + presigned URLs ([gravitywell.co.uk][8])                                                                                                                           | Armazenar MP4/4K, servir streaming     |
| **Banco**                | PostgreSQL com partição por *tenant\_id* ([crunchydata.com][9])                                                                                                                   | Users, Groups, Assets, Jobs            |
| **Ingestão**             | Node “ingestor” que conecta ao FTP (basic-ftp) ([npmjs.com][10], [stackoverflow.com][11])                                                                                         | Puxa JPEGs fresh da câmera             |

> **Diagrama resumido:** Browser ⇄ API ⇄ DB / Redis.  ⬇  API publica job ➜ BullMQ ➜ Worker chama FFmpeg ➜ salva MP4 no S3 ➜ callback atualiza DB ➜ Cliente recebe push/WS indicando “timelapse pronto”.

## 3. Pipeline de Processamento de Vídeo

1. **Pull de imagens** – agendado a cada X min.

   ```js
   const ftp = new Client();
   await ftp.access({ host, user, password, secure: true }); // FTPS
   await ftp.downloadToDir(localTmp, '/cam/images');
   ```

   ([npmjs.com][10], [stackoverflow.com][11])

2. **Seleção manual** – UI carrega thumbs (geradas on-the-fly) e o admin marca quadros chave.

3. **Timelapse job** – worker gera vídeo:

   ```bash
   ffmpeg -framerate 30 -pattern_type glob -i '*.jpg' -c:v libx264 -crf 18 -pix_fmt yuv420p out.mp4
   ```

   ([superuser.com][12], [medium.com][13], [gravitywell.co.uk][8])

4. **Render assíncrono** – BullMQ distribui carga; exemplo oficial mostra chunking para transcoding ([github.com][14], [blog.taskforce.sh][15]).

5. **Publicação** – MP4 sobe p/ S3; API grava row em `assets` com `visibility='public'` e `tenant_id`. Cliente recebe notificação WebSocket.

## 4. Modelo de Dados (PostgreSQL)

```sql
users (id, email, password_hash, role, tenant_id)
tenants (id, name)                 -- empresa
assets (id, tenant_id, path, type, status, created_at)
jobs (id, tenant_id, asset_id, status, payload, logs)
user_groups (user_id, tenant_id)   -- se precisar múltiplas empresas por user admin
```

*Indexe sempre por `tenant_id` para isolamento lógico.* ([crunchydata.com][9])

## 5. Interface de Edição

* **Grid de thumbs** carregado via Signed-URL → clique adiciona ao *timeline drawer*.
* **Preview instantâneo** com ffmpeg.wasm — o JS monta um MP4 baixo-res local antes de mandar p/ fila, dando feedback quase real ([github.com][3], [cloudinary.com][4]).
* Controles: FPS, ordem, corte de frames-início/fim.
* Botão **“Gerar Time-lapse”** dispara POST `/api/jobs`.

## 6. Segurança & Escalabilidade

* **Isolamento lógico**: queries sempre filtram `tenant_id`; use *Row-level Security* do Postgres opcionalmente.
* **Tráfego**: TLS em todas as camadas; FTP deve ser FTPS ou trocado por SFTP.
* **CDN**: CloudFront ou Cloudflare para servir MP4 4K sem gargalo.
* **Observabilidade**: BullMQ UI + Prometheus + Grafana para jobs; S3 access logs para métricas de clientes.

## 7. Roadmap MVP (4–6 semanas)

| Semana | Entrega                                                                                               |
| ------ | ----------------------------------------------------------------------------------------------------- |
| 1      | Setup repositório mono-repo (PNPM Workspaces), Postgres + Redis dockerizados, Auth0 integração básica |
| 2      | Ingestão FTP + fila BullMQ; API CRUD de tenants, users, assets                                        |
| 3      | UI React: login, listagem de assets, player Video.js                                                  |
| 4      | Tela de seleção de frames + pré-preview wasm                                                          |
| 5      | Worker FFmpeg produtivo em EC2 ou Fargate spot; upload S3; notificação                                |
| 6      | Hardening, logs, documentação e rollout para cliente piloto                                           |
