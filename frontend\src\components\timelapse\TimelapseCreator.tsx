import { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { useToastStore } from '../../stores/toastStore';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { AssetGrid } from './AssetGrid';
import { TimelapseSettings } from './TimelapseSettings';
import { SelectedFrames } from './SelectedFrames';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface Asset {
  id: string;
  name: string;
  type: string;
  file_path: string;
  file_size: number;
  metadata: any;
  created_at: string;
}

interface TimelapseCreatorProps {
  onClose?: () => void;
  onTimelapseCreated?: (jobId: string) => void;
}

interface TimelapseSettings {
  fps: number;
  quality: 'high' | 'medium' | 'low';
  resolution: '4k' | '1080p' | '720p';
  outputName: string;
}

export function TimelapseCreator({ onClose, onTimelapseCreated }: TimelapseCreatorProps) {
  const { token } = useAuthStore();
  const { success, error } = useToastStore();
  const [assets, setAssets] = useState<Asset[]>([]);
  const [selectedAssets, setSelectedAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [step, setStep] = useState<'select' | 'configure' | 'preview'>('select');
  const [settings, setSettings] = useState<TimelapseSettings>({
    fps: 30,
    quality: 'medium',
    resolution: '1080p',
    outputName: `timelapse-${new Date().toISOString().split('T')[0]}`
  });

  useEffect(() => {
    fetchAssets();
  }, []);

  const fetchAssets = async () => {
    try {
      const response = await fetch('/api/assets?type=image', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAssets(data.assets);
      }
    } catch (error) {
      console.error('Failed to fetch assets:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAssetSelect = (asset: Asset) => {
    setSelectedAssets(prev => {
      const isSelected = prev.find(a => a.id === asset.id);
      if (isSelected) {
        return prev.filter(a => a.id !== asset.id);
      } else {
        return [...prev, asset].sort((a, b) => 
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
      }
    });
  };

  const handleAssetReorder = (dragIndex: number, hoverIndex: number) => {
    const draggedAsset = selectedAssets[dragIndex];
    const newSelectedAssets = [...selectedAssets];
    newSelectedAssets.splice(dragIndex, 1);
    newSelectedAssets.splice(hoverIndex, 0, draggedAsset);
    setSelectedAssets(newSelectedAssets);
  };

  const handleRemoveAsset = (assetId: string) => {
    setSelectedAssets(prev => prev.filter(a => a.id !== assetId));
  };

  const handleCreateTimelapse = async () => {
    if (selectedAssets.length < 2) {
      error('Invalid Selection', 'Please select at least 2 images for the timelapse');
      return;
    }

    setCreating(true);

    try {
      const response = await fetch('/api/jobs/timelapse', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          assetIds: selectedAssets.map(a => a.id),
          settings
        }),
      });

      if (response.ok) {
        const data = await response.json();
        success(
          'Timelapse Job Created!',
          `Processing ${selectedAssets.length} frames. Check the Jobs page for progress.`,
          {
            action: {
              label: 'View Jobs',
              onClick: () => window.location.href = '/jobs'
            }
          }
        );

        if (onTimelapseCreated) {
          onTimelapseCreated(data.job.id);
        }
        if (onClose) {
          onClose();
        }
      } else {
        const errorData = await response.json();
        error('Failed to Create Timelapse', errorData.error);
      }
    } catch (err) {
      console.error('Failed to create timelapse:', err);
      error('Connection Error', 'Failed to create timelapse. Please try again.');
    } finally {
      setCreating(false);
    }
  };

  const getStepTitle = () => {
    switch (step) {
      case 'select': return 'Select Images';
      case 'configure': return 'Configure Settings';
      case 'preview': return 'Preview & Create';
      default: return 'Create Timelapse';
    }
  };

  const canProceed = () => {
    switch (step) {
      case 'select': return selectedAssets.length >= 2;
      case 'configure': return settings.outputName.trim().length > 0;
      case 'preview': return true;
      default: return false;
    }
  };

  const handleNext = () => {
    if (step === 'select') setStep('configure');
    else if (step === 'configure') setStep('preview');
    else if (step === 'preview') handleCreateTimelapse();
  };

  const handleBack = () => {
    if (step === 'configure') setStep('select');
    else if (step === 'preview') setStep('configure');
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white min-h-[90vh]">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">{getStepTitle()}</h3>
            <div className="flex items-center mt-2">
              {['select', 'configure', 'preview'].map((stepName, index) => (
                <div key={stepName} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step === stepName 
                      ? 'bg-primary-600 text-white'
                      : index < ['select', 'configure', 'preview'].indexOf(step)
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-600'
                  }`}>
                    {index + 1}
                  </div>
                  {index < 2 && (
                    <div className={`w-12 h-1 mx-2 ${
                      index < ['select', 'configure', 'preview'].indexOf(step)
                        ? 'bg-green-500'
                        : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              disabled={creating}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="flex-1">
          {loading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <>
              {step === 'select' && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <AssetGrid
                      assets={assets}
                      selectedAssets={selectedAssets}
                      onAssetSelect={handleAssetSelect}
                    />
                  </div>
                  <div>
                    <SelectedFrames
                      selectedAssets={selectedAssets}
                      onReorder={handleAssetReorder}
                      onRemove={handleRemoveAsset}
                    />
                  </div>
                </div>
              )}

              {step === 'configure' && (
                <div className="max-w-2xl mx-auto">
                  <TimelapseSettings
                    settings={settings}
                    onSettingsChange={setSettings}
                    selectedCount={selectedAssets.length}
                  />
                </div>
              )}

              {step === 'preview' && (
                <div className="max-w-4xl mx-auto">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-4">Timelapse Preview</h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h5 className="font-medium text-gray-700 mb-2">Settings</h5>
                        <dl className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <dt className="text-gray-500">Name:</dt>
                            <dd className="text-gray-900">{settings.outputName}</dd>
                          </div>
                          <div className="flex justify-between">
                            <dt className="text-gray-500">Frames:</dt>
                            <dd className="text-gray-900">{selectedAssets.length}</dd>
                          </div>
                          <div className="flex justify-between">
                            <dt className="text-gray-500">FPS:</dt>
                            <dd className="text-gray-900">{settings.fps}</dd>
                          </div>
                          <div className="flex justify-between">
                            <dt className="text-gray-500">Quality:</dt>
                            <dd className="text-gray-900 capitalize">{settings.quality}</dd>
                          </div>
                          <div className="flex justify-between">
                            <dt className="text-gray-500">Resolution:</dt>
                            <dd className="text-gray-900">{settings.resolution}</dd>
                          </div>
                          <div className="flex justify-between">
                            <dt className="text-gray-500">Duration:</dt>
                            <dd className="text-gray-900">
                              {(selectedAssets.length / settings.fps).toFixed(1)}s
                            </dd>
                          </div>
                        </dl>
                      </div>
                      
                      <div>
                        <h5 className="font-medium text-gray-700 mb-2">Frame Preview</h5>
                        <div className="grid grid-cols-4 gap-2">
                          {selectedAssets.slice(0, 8).map((asset, index) => (
                            <div key={asset.id} className="relative">
                              <div className="aspect-square bg-gray-200 rounded overflow-hidden">
                                <div className="w-full h-full flex items-center justify-center text-xs text-gray-500">
                                  Frame {index + 1}
                                </div>
                              </div>
                            </div>
                          ))}
                          {selectedAssets.length > 8 && (
                            <div className="aspect-square bg-gray-100 rounded flex items-center justify-center text-xs text-gray-500">
                              +{selectedAssets.length - 8} more
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center mt-8 pt-6 border-t">
          <div>
            {step !== 'select' && (
              <button
                onClick={handleBack}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                disabled={creating}
              >
                Back
              </button>
            )}
          </div>
          
          <div className="flex space-x-3">
            {selectedAssets.length > 0 && (
              <span className="text-sm text-gray-500 self-center">
                {selectedAssets.length} frame{selectedAssets.length !== 1 ? 's' : ''} selected
              </span>
            )}
            
            <button
              onClick={handleNext}
              disabled={!canProceed() || creating}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {creating && <LoadingSpinner size="sm" className="mr-2" />}
              {step === 'preview' 
                ? (creating ? 'Creating...' : 'Create Timelapse')
                : 'Next'
              }
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
