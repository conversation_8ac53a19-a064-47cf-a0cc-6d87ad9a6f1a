import { Router } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON> } from '../middleware/errorHandler';
import { query } from '../database/connection';
import { logger } from '../utils/logger';

const router = Router();

// Create or update user after Auth0 authentication
router.post('/callback', asyncHandler(async (req, res) => {
  const { auth0Id, email, name } = req.body;

  if (!auth0Id || !email || !name) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    // Check if user exists
    const existingUser = await query(
      'SELECT id, email, name, role, tenant_id, is_active FROM users WHERE auth0_id = $1',
      [auth0Id]
    );

    if (existingUser.rows.length > 0) {
      const user = existingUser.rows[0];
      
      // Update user info if needed
      await query(
        'UPDATE users SET email = $1, name = $2, last_login = NOW() WHERE auth0_id = $3',
        [email, name, auth0Id]
      );

      return res.json({
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          tenantId: user.tenant_id,
          isActive: user.is_active,
        }
      });
    }

    // For new users, they need to be invited by an admin
    // This endpoint just acknowledges the Auth0 callback
    return res.status(404).json({ 
      error: 'User not found. Please contact an administrator to get access.' 
    });

  } catch (error) {
    logger.error('Auth callback error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}));

// Get user profile
router.get('/profile', asyncHandler(async (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'No token provided' });
  }

  // This would typically be handled by the auth middleware
  // For now, return a placeholder response
  res.json({ message: 'Profile endpoint - implement with auth middleware' });
}));

export default router;
