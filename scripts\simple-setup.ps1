# Simple setup script for EagleView MVP

Write-Host "EagleView MVP - Simple Setup" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

Write-Host ""
Write-Host "Checking dependencies..." -ForegroundColor Yellow

# Check Node.js
if (Test-Command "node") {
    $nodeVersion = node --version
    Write-Host "Node.js: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "Node.js: NOT FOUND" -ForegroundColor Red
}

# Check PNPM
if (Test-Command "pnpm") {
    $pnpmVersion = pnpm --version
    Write-Host "PNPM: $pnpmVersion" -ForegroundColor Green
} else {
    Write-Host "PNPM: NOT FOUND" -ForegroundColor Red
}

# Check PostgreSQL
if (Test-Command "psql") {
    Write-Host "PostgreSQL: FOUND" -ForegroundColor Green
} else {
    Write-Host "PostgreSQL: NOT FOUND" -ForegroundColor Red
}

# Check Redis
if (Test-Command "redis-server") {
    Write-Host "Redis: FOUND" -ForegroundColor Green
} else {
    Write-Host "Redis: NOT FOUND" -ForegroundColor Red
}

# Check MinIO
if (Test-Command "minio") {
    Write-Host "MinIO: FOUND" -ForegroundColor Green
} else {
    Write-Host "MinIO: NOT FOUND" -ForegroundColor Red
}

# Check FFmpeg
if (Test-Command "ffmpeg") {
    Write-Host "FFmpeg: FOUND" -ForegroundColor Green
} else {
    Write-Host "FFmpeg: NOT FOUND" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Setting up environment files..." -ForegroundColor Yellow

# Copy environment files
if (!(Test-Path "api\.env")) {
    Copy-Item "api\.env.example" "api\.env"
    Write-Host "Created api\.env" -ForegroundColor Green
} else {
    Write-Host "api\.env already exists" -ForegroundColor Green
}

if (!(Test-Path "frontend\.env")) {
    Copy-Item "frontend\.env.example" "frontend\.env"
    Write-Host "Created frontend\.env" -ForegroundColor Green
} else {
    Write-Host "frontend\.env already exists" -ForegroundColor Green
}

# Create logs directory
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
    Write-Host "Created logs directory" -ForegroundColor Green
} else {
    Write-Host "logs directory already exists" -ForegroundColor Green
}

Write-Host ""
Write-Host "Setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Install missing dependencies (PostgreSQL, Redis, MinIO)" -ForegroundColor Cyan
Write-Host "2. Configure Auth0 credentials in .env files" -ForegroundColor Cyan
Write-Host "3. Start services and run: pnpm dev" -ForegroundColor Cyan
