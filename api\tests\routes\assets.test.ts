import { describe, it, expect, jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import assetsRouter from '../../src/routes/assets';
import { testData, createMockAuthRequest } from '../setup';
import { query } from '../../src/database/connection';

// Mock the auth middleware
jest.mock('../../src/middleware/auth', () => ({
  requireRole: (roles: string[]) => (req: any, res: any, next: any) => {
    if (req.user && roles.includes(req.user.role)) {
      next();
    } else {
      res.status(403).json({ error: 'Forbidden' });
    }
  }
}));

const app = express();
app.use(express.json());
app.use('/api/assets', assetsRouter);

describe('Assets API', () => {
  describe('GET /api/assets', () => {
    it('should return assets for admin user', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      // Mock the request object
      const response = await request(app)
        .get('/api/assets')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('assets');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.assets)).toBe(true);
    });

    it('should return only public assets for client user', async () => {
      const mockReq = createMockAuthRequest(testData.clientUserId, 'client');
      
      const response = await request(app)
        .get('/api/assets')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body.assets).toHaveLength(1); // Only public asset
      expect(response.body.assets[0].visibility).toBe('public');
    });

    it('should filter assets by type', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get('/api/assets?type=image')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      response.body.assets.forEach((asset: any) => {
        expect(asset.type).toBe('image');
      });
    });

    it('should paginate results', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get('/api/assets?page=1&limit=1')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body.assets).toHaveLength(1);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(1);
    });
  });

  describe('GET /api/assets/:id', () => {
    it('should return asset by ID for admin', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get(`/api/assets/${testData.imageAssetId}`)
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body.asset.id).toBe(testData.imageAssetId);
      expect(response.body.asset.name).toBe('test-image.jpg');
    });

    it('should return 404 for non-existent asset', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get('/api/assets/550e8400-e29b-41d4-a716-446655440999')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(404);
    });

    it('should not return private asset for client', async () => {
      const mockReq = createMockAuthRequest(testData.clientUserId, 'client');
      
      const response = await request(app)
        .get(`/api/assets/${testData.videoAssetId}`) // Private asset
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(404);
    });

    it('should return public asset for client', async () => {
      const mockReq = createMockAuthRequest(testData.clientUserId, 'client');
      
      const response = await request(app)
        .get(`/api/assets/${testData.imageAssetId}`) // Public asset
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body.asset.visibility).toBe('public');
    });
  });

  describe('GET /api/assets/:id/urls', () => {
    it('should return asset URLs for admin', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get(`/api/assets/${testData.imageAssetId}/urls`)
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('previewUrl');
      expect(response.body).toHaveProperty('downloadUrl');
      expect(response.body).toHaveProperty('thumbnailUrl');
      expect(response.body.previewUrl).toContain(testData.imageAssetId);
    });

    it('should return 404 for private asset accessed by client', async () => {
      const mockReq = createMockAuthRequest(testData.clientUserId, 'client');
      
      const response = await request(app)
        .get(`/api/assets/${testData.videoAssetId}/urls`)
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(404);
    });
  });

  describe('PUT /api/assets/:id', () => {
    it('should update asset for admin', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const updateData = {
        name: 'updated-image.jpg',
        visibility: 'private'
      };

      const response = await request(app)
        .put(`/api/assets/${testData.imageAssetId}`)
        .set('user', JSON.stringify(mockReq.user))
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.asset.name).toBe('updated-image.jpg');
      expect(response.body.asset.visibility).toBe('private');
    });

    it('should return 403 for client trying to update', async () => {
      const mockReq = createMockAuthRequest(testData.clientUserId, 'client');
      
      const response = await request(app)
        .put(`/api/assets/${testData.imageAssetId}`)
        .set('user', JSON.stringify(mockReq.user))
        .send({ name: 'hacked.jpg' });

      expect(response.status).toBe(403);
    });

    it('should return 404 for non-existent asset', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .put('/api/assets/550e8400-e29b-41d4-a716-446655440999')
        .set('user', JSON.stringify(mockReq.user))
        .send({ name: 'test.jpg' });

      expect(response.status).toBe(404);
    });

    it('should validate update data', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .put(`/api/assets/${testData.imageAssetId}`)
        .set('user', JSON.stringify(mockReq.user))
        .send({ visibility: 'invalid' });

      expect(response.status).toBe(400);
    });
  });

  describe('DELETE /api/assets/:id', () => {
    it('should delete asset for admin', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .delete(`/api/assets/${testData.videoAssetId}`)
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(204);

      // Verify asset is deleted
      const getResponse = await request(app)
        .get(`/api/assets/${testData.videoAssetId}`)
        .set('user', JSON.stringify(mockReq.user));

      expect(getResponse.status).toBe(404);
    });

    it('should return 403 for client trying to delete', async () => {
      const mockReq = createMockAuthRequest(testData.clientUserId, 'client');
      
      const response = await request(app)
        .delete(`/api/assets/${testData.imageAssetId}`)
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(403);
    });

    it('should return 404 for non-existent asset', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .delete('/api/assets/550e8400-e29b-41d4-a716-446655440999')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(404);
    });
  });
});
