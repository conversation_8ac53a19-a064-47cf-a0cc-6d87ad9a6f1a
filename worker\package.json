{"name": "@eagleview/worker", "version": "1.0.0", "description": "EagleView Worker - Video processing service", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src --ext .ts", "clean": "rm -rf dist"}, "dependencies": {"bullmq": "^4.15.0", "redis": "^4.6.10", "fluent-ffmpeg": "^2.1.2", "basic-ftp": "^5.0.4", "aws-sdk": "^2.1498.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "pg": "^8.11.3", "sharp": "^0.33.0", "fs-extra": "^11.2.0"}, "devDependencies": {"@types/fluent-ffmpeg": "^2.1.24", "@types/fs-extra": "^11.0.4", "@types/pg": "^8.10.9", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "tsx": "^4.6.0", "typescript": "^5.3.0"}}