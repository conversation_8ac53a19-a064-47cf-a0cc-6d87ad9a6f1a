import { describe, it, expect, jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import dashboardRouter from '../../src/routes/dashboard';
import { testData, createMockAuthRequest } from '../setup';

const app = express();
app.use(express.json());
app.use('/api/dashboard', dashboardRouter);

describe('Dashboard API', () => {
  describe('GET /api/dashboard/stats', () => {
    it('should return basic stats for client user', async () => {
      const mockReq = createMockAuthRequest(testData.clientUserId, 'client');
      
      const response = await request(app)
        .get('/api/dashboard/stats')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('totalAssets');
      expect(response.body).toHaveProperty('totalImages');
      expect(response.body).toHaveProperty('totalVideos');
      expect(response.body).toHaveProperty('totalTimelapses');
      expect(response.body).toHaveProperty('activeJobs');
      expect(response.body).toHaveProperty('completedJobs');
      expect(response.body).toHaveProperty('storageUsed');
      expect(response.body).toHaveProperty('storageLimit');
      
      // Client should not see admin stats
      expect(response.body).not.toHaveProperty('totalUsers');
      expect(response.body).not.toHaveProperty('totalTenants');
      expect(response.body).not.toHaveProperty('totalFtpSources');
    });

    it('should return admin stats for admin user', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get('/api/dashboard/stats')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('totalAssets');
      expect(response.body).toHaveProperty('totalUsers');
      expect(response.body).toHaveProperty('totalTenants');
      expect(response.body).toHaveProperty('totalFtpSources');
      
      // Verify numeric values
      expect(typeof response.body.totalAssets).toBe('number');
      expect(typeof response.body.totalUsers).toBe('number');
      expect(typeof response.body.storageUsed).toBe('number');
      expect(response.body.totalAssets).toBeGreaterThanOrEqual(0);
    });

    it('should return correct asset counts', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get('/api/dashboard/stats')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body.totalAssets).toBe(2); // From test data
      expect(response.body.totalImages).toBe(1);
      expect(response.body.totalVideos).toBe(1);
      expect(response.body.totalTimelapses).toBe(0);
    });
  });

  describe('GET /api/dashboard/activity', () => {
    it('should return recent activity', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get('/api/dashboard/activity')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('activities');
      expect(Array.isArray(response.body.activities)).toBe(true);
    });

    it('should limit activity results', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get('/api/dashboard/activity?limit=5')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      expect(response.body.activities.length).toBeLessThanOrEqual(5);
    });

    it('should include activity metadata', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get('/api/dashboard/activity')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      
      if (response.body.activities.length > 0) {
        const activity = response.body.activities[0];
        expect(activity).toHaveProperty('id');
        expect(activity).toHaveProperty('type');
        expect(activity).toHaveProperty('title');
        expect(activity).toHaveProperty('description');
        expect(activity).toHaveProperty('timestamp');
      }
    });

    it('should filter activity by tenant', async () => {
      const mockReq = createMockAuthRequest(testData.clientUserId, 'client');
      
      const response = await request(app)
        .get('/api/dashboard/activity')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(200);
      // All activities should belong to the user's tenant
      // This is implicitly tested by the database query filtering
    });
  });

  describe('Dashboard Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock database error
      jest.spyOn(require('../../src/database/connection'), 'query')
        .mockRejectedValueOnce(new Error('Database connection failed'));

      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const response = await request(app)
        .get('/api/dashboard/stats')
        .set('user', JSON.stringify(mockReq.user));

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Dashboard Performance', () => {
    it('should respond within reasonable time', async () => {
      const mockReq = createMockAuthRequest(testData.adminUserId, 'admin');
      
      const startTime = Date.now();
      const response = await request(app)
        .get('/api/dashboard/stats')
        .set('user', JSON.stringify(mockReq.user));
      const endTime = Date.now();

      expect(response.status).toBe(200);
      expect(endTime - startTime).toBeLessThan(1000); // Should respond within 1 second
    });
  });
});
