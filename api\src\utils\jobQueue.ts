import { Queue } from 'bullmq';
import { getRedisClient } from './redis';
import { logger } from './logger';

// Job queues
let timelapseQueue: Queue;
let thumbnailQueue: Queue;
let ftpSyncQueue: Queue;

export async function initializeQueues() {
  const redisConnection = getRedisClient();

  // Initialize queues
  timelapseQueue = new Queue('timelapse', { connection: redisConnection });
  thumbnailQueue = new Queue('thumbnail', { connection: redisConnection });
  ftpSyncQueue = new Queue('ftp-sync', { connection: redisConnection });

  logger.info('Job queues initialized successfully');
}

export function getTimelapseQueue(): Queue {
  if (!timelapseQueue) {
    throw new Error('Timelapse queue not initialized');
  }
  return timelapseQueue;
}

export function getThumbnailQueue(): Queue {
  if (!thumbnailQueue) {
    throw new Error('Thumbnail queue not initialized');
  }
  return thumbnailQueue;
}

export function getFtpSyncQueue(): Queue {
  if (!ftpSyncQueue) {
    throw new Error('FTP sync queue not initialized');
  }
  return ftpSyncQueue;
}

// Job scheduling functions
export async function scheduleTimelapseJob(data: {
  jobId: string;
  tenantId: string;
  assetIds: string[];
  settings: {
    fps: number;
    quality: 'high' | 'medium' | 'low';
    resolution: '4k' | '1080p' | '720p';
    outputName: string;
  };
}) {
  const job = await timelapseQueue.add('process-timelapse', data, {
    jobId: data.jobId,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000,
    },
    removeOnComplete: 10,
    removeOnFail: 5,
  });

  logger.info(`Scheduled timelapse job: ${job.id}`);
  return job;
}

export async function scheduleThumbnailJob(data: {
  jobId: string;
  tenantId: string;
  assetId: string;
  sizes: Array<{
    name: string;
    width: number;
    height: number;
    quality: number;
  }>;
}) {
  const job = await thumbnailQueue.add('process-thumbnail', data, {
    jobId: data.jobId,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    removeOnComplete: 20,
    removeOnFail: 10,
  });

  logger.info(`Scheduled thumbnail job: ${job.id}`);
  return job;
}

export async function scheduleFtpSyncJob(data: {
  jobId: string;
  tenantId: string;
  ftpSourceId: string;
  syncPath?: string;
}) {
  const job = await ftpSyncQueue.add('process-ftp-sync', data, {
    jobId: data.jobId,
    attempts: 2,
    backoff: {
      type: 'fixed',
      delay: 10000,
    },
    removeOnComplete: 5,
    removeOnFail: 3,
  });

  logger.info(`Scheduled FTP sync job: ${job.id}`);
  return job;
}

// Recurring job scheduling
export async function scheduleRecurringFtpSync(
  tenantId: string,
  ftpSourceId: string,
  cronPattern: string = '*/15 * * * *' // Every 15 minutes by default
) {
  const jobId = `ftp-sync-${tenantId}-${ftpSourceId}`;
  
  const job = await ftpSyncQueue.add(
    'process-ftp-sync',
    {
      jobId,
      tenantId,
      ftpSourceId,
    },
    {
      repeat: {
        pattern: cronPattern,
      },
      jobId,
    }
  );

  logger.info(`Scheduled recurring FTP sync: ${jobId} with pattern: ${cronPattern}`);
  return job;
}

// Job management functions
export async function getJobStatus(queueName: string, jobId: string) {
  let queue: Queue;
  
  switch (queueName) {
    case 'timelapse':
      queue = timelapseQueue;
      break;
    case 'thumbnail':
      queue = thumbnailQueue;
      break;
    case 'ftp-sync':
      queue = ftpSyncQueue;
      break;
    default:
      throw new Error(`Unknown queue: ${queueName}`);
  }

  const job = await queue.getJob(jobId);
  
  if (!job) {
    return null;
  }

  return {
    id: job.id,
    name: job.name,
    data: job.data,
    progress: job.progress,
    processedOn: job.processedOn,
    finishedOn: job.finishedOn,
    failedReason: job.failedReason,
    returnvalue: job.returnvalue,
  };
}

export async function cancelJob(queueName: string, jobId: string) {
  let queue: Queue;
  
  switch (queueName) {
    case 'timelapse':
      queue = timelapseQueue;
      break;
    case 'thumbnail':
      queue = thumbnailQueue;
      break;
    case 'ftp-sync':
      queue = ftpSyncQueue;
      break;
    default:
      throw new Error(`Unknown queue: ${queueName}`);
  }

  const job = await queue.getJob(jobId);
  
  if (job) {
    await job.remove();
    logger.info(`Cancelled job: ${jobId} from queue: ${queueName}`);
    return true;
  }
  
  return false;
}

export async function retryJob(queueName: string, jobId: string) {
  let queue: Queue;
  
  switch (queueName) {
    case 'timelapse':
      queue = timelapseQueue;
      break;
    case 'thumbnail':
      queue = thumbnailQueue;
      break;
    case 'ftp-sync':
      queue = ftpSyncQueue;
      break;
    default:
      throw new Error(`Unknown queue: ${queueName}`);
  }

  const job = await queue.getJob(jobId);
  
  if (job) {
    await job.retry();
    logger.info(`Retried job: ${jobId} from queue: ${queueName}`);
    return true;
  }
  
  return false;
}

// Queue statistics
export async function getQueueStats() {
  const stats = {
    timelapse: await timelapseQueue.getJobCounts(),
    thumbnail: await thumbnailQueue.getJobCounts(),
    ftpSync: await ftpSyncQueue.getJobCounts(),
  };

  return stats;
}

// Cleanup functions
export async function closeQueues() {
  if (timelapseQueue) {
    await timelapseQueue.close();
  }
  if (thumbnailQueue) {
    await thumbnailQueue.close();
  }
  if (ftpSyncQueue) {
    await ftpSyncQueue.close();
  }
  
  logger.info('Job queues closed');
}
