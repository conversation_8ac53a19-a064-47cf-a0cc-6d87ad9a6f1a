import { Router } from 'express';
import { z } from 'zod';
import crypto from 'crypto';
import { async<PERSON><PERSON><PERSON>, createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import { query } from '../database/connection';
import { logger } from '../utils/logger';
import { scheduleFtpSyncJob, scheduleRecurringFtpSync } from '../utils/jobQueue';

const router = Router();

const createFtpSourceSchema = z.object({
  name: z.string().min(1).max(255),
  host: z.string().min(1).max(255),
  port: z.number().min(1).max(65535).default(21),
  username: z.string().min(1).max(255),
  password: z.string().min(1),
  path: z.string().default('/'),
  isSecure: z.boolean().default(false),
  syncSchedule: z.string().optional(), // <PERSON>ron pattern
});

const updateFtpSourceSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  host: z.string().min(1).max(255).optional(),
  port: z.number().min(1).max(65535).optional(),
  username: z.string().min(1).max(255).optional(),
  password: z.string().min(1).optional(),
  path: z.string().optional(),
  isSecure: z.boolean().optional(),
  isActive: z.boolean().optional(),
  syncSchedule: z.string().optional(),
});

// Get FTP sources (admin only)
router.get('/', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const result = await query(
    `SELECT id, name, host, port, username, path, is_secure, is_active, last_sync, created_at, updated_at
     FROM ftp_sources 
     WHERE tenant_id = $1 
     ORDER BY name`,
    [req.user?.tenantId]
  );

  res.json({
    ftpSources: result.rows
  });
}));

// Get FTP source by ID (admin only)
router.get('/:id', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  
  const result = await query(
    `SELECT id, name, host, port, username, path, is_secure, is_active, last_sync, created_at, updated_at
     FROM ftp_sources 
     WHERE id = $1 AND tenant_id = $2`,
    [id, req.user?.tenantId]
  );

  if (result.rows.length === 0) {
    throw createError('FTP source not found', 404);
  }

  res.json({
    ftpSource: result.rows[0]
  });
}));

// Create FTP source (admin only)
router.post('/', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createFtpSourceSchema.parse(req.body);

  // Encrypt password (in production, use proper encryption)
  const encryptedPassword = encryptPassword(validatedData.password);

  const result = await query(
    `INSERT INTO ftp_sources (tenant_id, name, host, port, username, password_encrypted, path, is_secure) 
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
     RETURNING id, name, host, port, username, path, is_secure, is_active, created_at`,
    [
      req.user?.tenantId,
      validatedData.name,
      validatedData.host,
      validatedData.port,
      validatedData.username,
      encryptedPassword,
      validatedData.path,
      validatedData.isSecure
    ]
  );

  const ftpSource = result.rows[0];

  // Schedule recurring sync if pattern provided
  if (validatedData.syncSchedule) {
    try {
      await scheduleRecurringFtpSync(
        req.user?.tenantId!,
        ftpSource.id,
        validatedData.syncSchedule
      );
      logger.info(`Scheduled recurring FTP sync for source: ${ftpSource.id}`);
    } catch (error) {
      logger.warn(`Failed to schedule recurring sync: ${error.message}`);
    }
  }

  logger.info(`FTP source created: ${validatedData.name} by user ${req.user?.id}`);

  res.status(201).json({
    ftpSource
  });
}));

// Update FTP source (admin only)
router.put('/:id', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const validatedData = updateFtpSourceSchema.parse(req.body);

  // Check if FTP source exists
  const existingSource = await query(
    'SELECT id FROM ftp_sources WHERE id = $1 AND tenant_id = $2',
    [id, req.user?.tenantId]
  );

  if (existingSource.rows.length === 0) {
    throw createError('FTP source not found', 404);
  }

  const updateFields = [];
  const updateValues = [];
  let paramCount = 1;

  if (validatedData.name) {
    updateFields.push(`name = $${paramCount++}`);
    updateValues.push(validatedData.name);
  }

  if (validatedData.host) {
    updateFields.push(`host = $${paramCount++}`);
    updateValues.push(validatedData.host);
  }

  if (validatedData.port) {
    updateFields.push(`port = $${paramCount++}`);
    updateValues.push(validatedData.port);
  }

  if (validatedData.username) {
    updateFields.push(`username = $${paramCount++}`);
    updateValues.push(validatedData.username);
  }

  if (validatedData.password) {
    updateFields.push(`password_encrypted = $${paramCount++}`);
    updateValues.push(encryptPassword(validatedData.password));
  }

  if (validatedData.path) {
    updateFields.push(`path = $${paramCount++}`);
    updateValues.push(validatedData.path);
  }

  if (validatedData.isSecure !== undefined) {
    updateFields.push(`is_secure = $${paramCount++}`);
    updateValues.push(validatedData.isSecure);
  }

  if (validatedData.isActive !== undefined) {
    updateFields.push(`is_active = $${paramCount++}`);
    updateValues.push(validatedData.isActive);
  }

  if (updateFields.length === 0) {
    throw createError('No fields to update', 400);
  }

  updateValues.push(id);

  const result = await query(
    `UPDATE ftp_sources SET ${updateFields.join(', ')} WHERE id = $${paramCount} 
     RETURNING id, name, host, port, username, path, is_secure, is_active, last_sync, created_at, updated_at`,
    updateValues
  );

  logger.info(`FTP source updated: ${id} by user ${req.user?.id}`);

  res.json({
    ftpSource: result.rows[0]
  });
}));

// Test FTP connection (admin only)
router.post('/:id/test', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Get FTP source
  const ftpResult = await query(
    'SELECT * FROM ftp_sources WHERE id = $1 AND tenant_id = $2',
    [id, req.user?.tenantId]
  );

  if (ftpResult.rows.length === 0) {
    throw createError('FTP source not found', 404);
  }

  const ftpSource = ftpResult.rows[0];

  // Create a test sync job
  const jobId = crypto.randomUUID();
  
  await query(
    `INSERT INTO jobs (id, tenant_id, type, payload, created_by) 
     VALUES ($1, $2, 'ftp-sync', $3, $4)`,
    [
      jobId,
      req.user?.tenantId,
      JSON.stringify({ 
        ftpSourceId: id, 
        testConnection: true 
      }),
      req.user?.id
    ]
  );

  // Schedule test job
  await scheduleFtpSyncJob({
    jobId,
    tenantId: req.user?.tenantId!,
    ftpSourceId: id
  });

  logger.info(`FTP connection test initiated: ${id} by user ${req.user?.id}`);

  res.json({
    message: 'FTP connection test initiated',
    jobId
  });
}));

// Trigger manual sync (admin only)
router.post('/:id/sync', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const { syncPath } = req.body;

  // Check if FTP source exists and is active
  const ftpResult = await query(
    'SELECT id FROM ftp_sources WHERE id = $1 AND tenant_id = $2 AND is_active = true',
    [id, req.user?.tenantId]
  );

  if (ftpResult.rows.length === 0) {
    throw createError('FTP source not found or inactive', 404);
  }

  // Create sync job
  const jobId = crypto.randomUUID();
  
  await query(
    `INSERT INTO jobs (id, tenant_id, type, payload, created_by) 
     VALUES ($1, $2, 'ftp-sync', $3, $4)`,
    [
      jobId,
      req.user?.tenantId,
      JSON.stringify({ 
        ftpSourceId: id, 
        syncPath: syncPath || undefined 
      }),
      req.user?.id
    ]
  );

  // Schedule sync job
  await scheduleFtpSyncJob({
    jobId,
    tenantId: req.user?.tenantId!,
    ftpSourceId: id,
    syncPath
  });

  logger.info(`Manual FTP sync initiated: ${id} by user ${req.user?.id}`);

  res.json({
    message: 'FTP sync initiated',
    jobId
  });
}));

// Delete FTP source (admin only)
router.delete('/:id', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if FTP source exists
  const existingSource = await query(
    'SELECT id, name FROM ftp_sources WHERE id = $1 AND tenant_id = $2',
    [id, req.user?.tenantId]
  );

  if (existingSource.rows.length === 0) {
    throw createError('FTP source not found', 404);
  }

  await query('DELETE FROM ftp_sources WHERE id = $1', [id]);

  logger.info(`FTP source deleted: ${existingSource.rows[0].name} (${id}) by user ${req.user?.id}`);

  res.status(204).send();
}));

function encryptPassword(password: string): string {
  // TODO: Implement proper encryption in production
  // For MVP, use base64 encoding
  return Buffer.from(password, 'utf8').toString('base64');
}

export default router;
