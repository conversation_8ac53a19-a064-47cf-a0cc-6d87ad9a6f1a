import { Job } from 'bullmq';
import sharp from 'sharp';
import path from 'path';
import fs from 'fs-extra';
import { logger } from '../utils/logger';
import { query } from '../database/connection';
import { uploadToS3 } from '../utils/s3Client';

interface ThumbnailJobData {
  jobId: string;
  tenantId: string;
  assetId: string;
  sizes: Array<{
    name: string;
    width: number;
    height: number;
    quality: number;
  }>;
}

export async function processThumbnailJob(job: Job<ThumbnailJobData>) {
  const { jobId, tenantId, assetId, sizes } = job.data;
  
  logger.info(`Starting thumbnail job ${jobId} for asset ${assetId}`);
  
  try {
    // Update job status to processing
    await updateJobStatus(jobId, 'processing', 0);
    
    // Get asset information
    const assetResult = await query(
      'SELECT id, file_path, name, type FROM assets WHERE id = $1 AND tenant_id = $2',
      [assetId, tenantId]
    );
    
    if (assetResult.rows.length === 0) {
      throw new Error('Asset not found');
    }
    
    const asset = assetResult.rows[0];
    
    if (asset.type !== 'image') {
      throw new Error('Thumbnail generation only supported for images');
    }
    
    // Create temporary directory
    const tempDir = path.join(__dirname, '../../temp', jobId);
    await fs.ensureDir(tempDir);
    
    // Download original image from S3 to temp directory
    const originalPath = path.join(tempDir, 'original.jpg');
    
    // TODO: Download from S3 - for now assume local files
    const sourcePath = path.join(__dirname, '../../..', asset.file_path);
    if (!(await fs.pathExists(sourcePath))) {
      throw new Error(`Source file not found: ${sourcePath}`);
    }
    
    await fs.copy(sourcePath, originalPath);
    await job.updateProgress(20);
    
    // Generate thumbnails for each size
    const thumbnails: Array<{
      name: string;
      s3Key: string;
      s3Url: string;
      width: number;
      height: number;
      size: number;
    }> = [];
    
    for (let i = 0; i < sizes.length; i++) {
      const size = sizes[i];
      const thumbnailPath = path.join(tempDir, `${size.name}.jpg`);
      
      logger.info(`Generating ${size.name} thumbnail: ${size.width}x${size.height}`);
      
      // Generate thumbnail with Sharp
      await sharp(originalPath)
        .resize(size.width, size.height, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({
          quality: size.quality,
          progressive: true
        })
        .toFile(thumbnailPath);
      
      // Upload to S3
      const s3Key = `thumbnails/${tenantId}/${assetId}/${size.name}.jpg`;
      const s3Url = await uploadToS3(thumbnailPath, s3Key);
      
      const stats = await fs.stat(thumbnailPath);
      
      thumbnails.push({
        name: size.name,
        s3Key,
        s3Url,
        width: size.width,
        height: size.height,
        size: stats.size
      });
      
      await job.updateProgress(20 + ((i + 1) / sizes.length) * 60);
    }
    
    // Update asset metadata with thumbnail information
    const currentMetadata = asset.metadata || {};
    const updatedMetadata = {
      ...currentMetadata,
      thumbnails: thumbnails.reduce((acc, thumb) => {
        acc[thumb.name] = {
          s3Key: thumb.s3Key,
          s3Url: thumb.s3Url,
          width: thumb.width,
          height: thumb.height,
          size: thumb.size
        };
        return acc;
      }, {} as any)
    };
    
    await query(
      'UPDATE assets SET metadata = $1 WHERE id = $2',
      [JSON.stringify(updatedMetadata), assetId]
    );
    
    // Update job with result
    await updateJobStatus(jobId, 'completed', 100, {
      assetId,
      thumbnails: thumbnails.map(t => ({
        name: t.name,
        s3Url: t.s3Url,
        width: t.width,
        height: t.height
      }))
    });
    
    // Cleanup temp directory
    await fs.remove(tempDir);
    
    logger.info(`Thumbnail job ${jobId} completed successfully`);
    
    // Notify via Socket.IO
    if (global.io) {
      global.io.to(`tenant-${tenantId}`).emit('job-completed', {
        jobId,
        type: 'thumbnail',
        assetId
      });
    }
    
  } catch (error) {
    logger.error(`Thumbnail job ${jobId} failed:`, error);
    
    await updateJobStatus(jobId, 'failed', job.progress || 0, null, error.message);
    
    // Notify via Socket.IO
    if (global.io) {
      global.io.to(`tenant-${tenantId}`).emit('job-failed', {
        jobId,
        type: 'thumbnail',
        error: error.message
      });
    }
    
    throw error;
  }
}

async function updateJobStatus(
  jobId: string, 
  status: string, 
  progress: number, 
  result?: any, 
  errorMessage?: string
) {
  const updateFields = ['status = $2', 'progress = $3'];
  const updateValues = [jobId, status, progress];
  let paramCount = 4;
  
  if (status === 'processing' && !errorMessage) {
    updateFields.push('started_at = NOW()');
  }
  
  if (status === 'completed' || status === 'failed') {
    updateFields.push('completed_at = NOW()');
  }
  
  if (result) {
    updateFields.push(`result = $${paramCount++}`);
    updateValues.push(JSON.stringify(result));
  }
  
  if (errorMessage) {
    updateFields.push(`error_message = $${paramCount++}`);
    updateValues.push(errorMessage);
  }
  
  await query(
    `UPDATE jobs SET ${updateFields.join(', ')} WHERE id = $1`,
    updateValues
  );
}
