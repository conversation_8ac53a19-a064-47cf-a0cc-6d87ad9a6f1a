import { Router } from 'express';
import { z } from 'zod';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import { query } from '../database/connection';
import { logger } from '../utils/logger';

const router = Router();

const createTenantSchema = z.object({
  name: z.string().min(1).max(255),
  slug: z.string().min(1).max(100).regex(/^[a-z0-9-]+$/),
});

const updateTenantSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  settings: z.object({}).optional(),
});

// Get all tenants (admin only)
router.get('/', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const result = await query(
    'SELECT id, name, slug, settings, created_at, updated_at FROM tenants ORDER BY name'
  );

  res.json({
    tenants: result.rows
  });
}));

// Get current user's tenant
router.get('/current', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user?.tenantId) {
    throw createError('User not associated with any tenant', 404);
  }

  const result = await query(
    'SELECT id, name, slug, settings, created_at, updated_at FROM tenants WHERE id = $1',
    [req.user.tenantId]
  );

  if (result.rows.length === 0) {
    throw createError('Tenant not found', 404);
  }

  res.json({
    tenant: result.rows[0]
  });
}));

// Create tenant (admin only)
router.post('/', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createTenantSchema.parse(req.body);

  // Check if slug already exists
  const existingTenant = await query(
    'SELECT id FROM tenants WHERE slug = $1',
    [validatedData.slug]
  );

  if (existingTenant.rows.length > 0) {
    throw createError('Tenant slug already exists', 409);
  }

  const result = await query(
    'INSERT INTO tenants (name, slug) VALUES ($1, $2) RETURNING id, name, slug, settings, created_at, updated_at',
    [validatedData.name, validatedData.slug]
  );

  logger.info(`Tenant created: ${validatedData.name} (${validatedData.slug}) by user ${req.user?.id}`);

  res.status(201).json({
    tenant: result.rows[0]
  });
}));

// Update tenant
router.put('/:id', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const validatedData = updateTenantSchema.parse(req.body);

  // Check if tenant exists
  const existingTenant = await query(
    'SELECT id FROM tenants WHERE id = $1',
    [id]
  );

  if (existingTenant.rows.length === 0) {
    throw createError('Tenant not found', 404);
  }

  const updateFields = [];
  const updateValues = [];
  let paramCount = 1;

  if (validatedData.name) {
    updateFields.push(`name = $${paramCount++}`);
    updateValues.push(validatedData.name);
  }

  if (validatedData.settings) {
    updateFields.push(`settings = $${paramCount++}`);
    updateValues.push(JSON.stringify(validatedData.settings));
  }

  if (updateFields.length === 0) {
    throw createError('No fields to update', 400);
  }

  updateValues.push(id);

  const result = await query(
    `UPDATE tenants SET ${updateFields.join(', ')} WHERE id = $${paramCount} RETURNING id, name, slug, settings, created_at, updated_at`,
    updateValues
  );

  logger.info(`Tenant updated: ${id} by user ${req.user?.id}`);

  res.json({
    tenant: result.rows[0]
  });
}));

// Delete tenant (admin only)
router.delete('/:id', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if tenant exists
  const existingTenant = await query(
    'SELECT id, name FROM tenants WHERE id = $1',
    [id]
  );

  if (existingTenant.rows.length === 0) {
    throw createError('Tenant not found', 404);
  }

  // Check if tenant has users
  const usersCount = await query(
    'SELECT COUNT(*) as count FROM users WHERE tenant_id = $1',
    [id]
  );

  if (parseInt(usersCount.rows[0].count) > 0) {
    throw createError('Cannot delete tenant with existing users', 409);
  }

  await query('DELETE FROM tenants WHERE id = $1', [id]);

  logger.info(`Tenant deleted: ${existingTenant.rows[0].name} (${id}) by user ${req.user?.id}`);

  res.status(204).send();
}));

export default router;
