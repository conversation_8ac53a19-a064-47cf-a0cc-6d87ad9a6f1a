# 🚀 EagleView MVP - Configuração Local (Sem Docker)

Este guia explica como executar o EagleView MVP localmente no Windows sem usar Docker.

## 📋 Pré-requisitos

### Obrigatórios
- ✅ **Node.js 18+** (j<PERSON> instalado)
- ✅ **PNPM 8+** (j<PERSON> instalado)
- **PostgreSQL 15+**
- **Redis 7+**
- **MinIO Server**

### Opcionais
- **FFmpeg** (para processamento de vídeo)
- **Chocolatey** (para instalação fácil dos serviços)

## 🔧 Instalação dos Serviços

### Opção 1: Via Chocolatey (Recomendado)

```powershell
# Instalar Chocolatey (se não tiver)
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Instalar serviços
choco install postgresql redis-64 minio ffmpeg
```

### Opção 2: Download Manual

#### PostgreSQL
- Download: https://www.postgresql.org/download/windows/
- Instale com as configurações padrão
- Lembre-se da senha do usuário `postgres`

#### Redis
- Download: https://github.com/microsoftarchive/redis/releases
- Ou use WSL: `wsl --install` e depois `sudo apt install redis-server`

#### MinIO
- Download: https://min.io/download#/windows
- Extraia o executável para uma pasta no PATH

#### FFmpeg
- Download: https://ffmpeg.org/download.html#build-windows
- Extraia e adicione ao PATH do Windows

## ⚡ Início Rápido

### 1. Executar Script Automático

```powershell
# Navegar para o diretório do projeto
cd e:\1-GITHUB\EagleViewMVP

# Executar script de configuração completa
.\scripts\start-local.ps1
```

### 2. Configuração Manual (Passo a Passo)

#### Passo 1: Configurar Ambiente
```powershell
# Executar script de setup
.\scripts\setup-local-windows.ps1
```

#### Passo 2: Iniciar Serviços de Infraestrutura
```powershell
# Iniciar todos os serviços
.\scripts\start-services-local.ps1
```

#### Passo 3: Configurar Banco de Dados
```powershell
# Configurar PostgreSQL
.\scripts\setup-database-local.ps1

# Executar migrações
pnpm db:migrate
```

#### Passo 4: Iniciar Aplicação
```powershell
# Instalar dependências (se necessário)
pnpm install

# Construir aplicação
pnpm build

# Iniciar servidores de desenvolvimento
pnpm dev
```

## 🔧 Configuração Manual dos Serviços

### PostgreSQL

1. **Iniciar Serviço**:
   ```powershell
   net start postgresql-x64-15
   ```

2. **Criar Banco e Usuário**:
   ```sql
   -- Conectar como postgres
   psql -U postgres

   -- Criar usuário
   CREATE USER eagleview WITH PASSWORD 'eagleview_dev_password';
   GRANT CREATEDB TO eagleview;

   -- Criar banco
   CREATE DATABASE eagleview OWNER eagleview;
   ```

### Redis

1. **Iniciar Redis**:
   ```powershell
   redis-server
   ```

2. **Verificar Conexão**:
   ```powershell
   redis-cli ping
   # Deve retornar: PONG
   ```

### MinIO

1. **Configurar Credenciais**:
   ```powershell
   $env:MINIO_ROOT_USER = "eagleview"
   $env:MINIO_ROOT_PASSWORD = "eagleview_dev_password"
   ```

2. **Iniciar MinIO**:
   ```powershell
   # Criar diretório de dados
   mkdir minio_data

   # Iniciar servidor
   minio server minio_data --console-address ":9001"
   ```

## 📁 Configuração de Ambiente

### Arquivo `api\.env`
```bash
NODE_ENV=development
PORT=3001
DATABASE_URL=postgresql://eagleview:eagleview_dev_password@localhost:5432/eagleview
REDIS_URL=redis://localhost:6379
AUTH0_DOMAIN=your-auth0-domain.auth0.com
AUTH0_AUDIENCE=https://api.eagleview.com
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_CLIENT_SECRET=your-auth0-client-secret
AWS_ACCESS_KEY_ID=eagleview
AWS_SECRET_ACCESS_KEY=eagleview_dev_password
S3_ENDPOINT=http://localhost:9000
S3_BUCKET=eagleview-assets
FFMPEG_PATH=ffmpeg
```

### Arquivo `frontend\.env`
```bash
VITE_AUTH0_DOMAIN=your-auth0-domain.auth0.com
VITE_AUTH0_CLIENT_ID=your-auth0-client-id
VITE_AUTH0_AUDIENCE=https://api.eagleview.com
VITE_API_BASE_URL=http://localhost:3001/api
VITE_SOCKET_URL=http://localhost:3001
```

## 🌐 URLs da Aplicação

Após iniciar todos os serviços:

- **Frontend**: http://localhost:3000
- **API**: http://localhost:3001
- **API Health**: http://localhost:3001/health
- **MinIO Console**: http://localhost:9001
  - Login: `eagleview` / `eagleview_dev_password`

## 🔍 Verificação dos Serviços

### Verificar se as Portas Estão Ativas
```powershell
# PostgreSQL (porta 5432)
Test-NetConnection -ComputerName localhost -Port 5432

# Redis (porta 6379)
Test-NetConnection -ComputerName localhost -Port 6379

# MinIO (porta 9000)
Test-NetConnection -ComputerName localhost -Port 9000
```

### Verificar Logs
```powershell
# Ver logs da aplicação
# Os logs aparecerão no terminal onde você executou `pnpm dev`

# Ver logs do PostgreSQL
# Verifique os logs no diretório de instalação do PostgreSQL

# Ver logs do Redis
# Redis mostra logs no terminal onde foi iniciado
```

## 🛠️ Solução de Problemas

### Erro: "Port already in use"
```powershell
# Verificar qual processo está usando a porta
netstat -ano | findstr :3001

# Matar processo se necessário
taskkill /PID <PID> /F
```

### Erro: "Cannot connect to database"
1. Verificar se PostgreSQL está rodando
2. Verificar credenciais no arquivo `.env`
3. Testar conexão manual: `psql -U eagleview -d eagleview`

### Erro: "Redis connection failed"
1. Verificar se Redis está rodando
2. Testar conexão: `redis-cli ping`

### Erro: "MinIO not accessible"
1. Verificar se MinIO está rodando na porta 9000
2. Verificar credenciais de ambiente
3. Acessar console: http://localhost:9001

## 📝 Comandos Úteis

```powershell
# Parar todos os serviços
# Pressione Ctrl+C no terminal dos servidores de desenvolvimento

# Reiniciar aplicação
pnpm dev

# Executar migrações
pnpm db:migrate

# Executar seeds
pnpm db:seed

# Executar testes
pnpm test

# Limpar build
pnpm clean

# Verificar lint
pnpm lint
```

## 🔄 Próximos Passos

1. **Configurar Auth0**: Atualize os arquivos `.env` com suas credenciais do Auth0
2. **Testar Aplicação**: Acesse http://localhost:3000
3. **Configurar FTP** (opcional): Configure fontes FTP no arquivo `.env`
4. **Executar Testes**: Execute `pnpm test` para verificar se tudo está funcionando

## 📚 Documentação Adicional

- [Configuração do Auth0](docs/AUTH0_SETUP.md)
- [Documentação da API](docs/API.md)
- [Guia de Desenvolvimento](docs/DEVELOPMENT.md)
