import { beforeAll, afterAll, beforeEach } from '@jest/globals';
import { query } from '../src/database/connection';

// Test database setup
beforeAll(async () => {
  // Create test tables if they don't exist
  await createTestTables();
});

afterAll(async () => {
  // Clean up test data
  await cleanupTestData();
});

beforeEach(async () => {
  // Clean up data before each test
  await cleanupTestData();
  // Insert test data
  await insertTestData();
});

async function createTestTables() {
  // Create tenants table
  await query(`
    CREATE TABLE IF NOT EXISTS tenants (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(255) UNIQUE NOT NULL,
      display_name VARCHAR(255) NOT NULL,
      domain VARCHAR(255),
      description TEXT,
      settings JSONB DEFAULT '{}',
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create users table
  await query(`
    CREATE TABLE IF NOT EXISTS users (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
      email VARCHAR(255) UNIQUE NOT NULL,
      name VARCHAR(255) NOT NULL,
      role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'client')),
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create assets table
  await query(`
    CREATE TABLE IF NOT EXISTS assets (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
      name VARCHAR(255) NOT NULL,
      type VARCHAR(50) NOT NULL CHECK (type IN ('image', 'video', 'timelapse')),
      file_path VARCHAR(500) NOT NULL,
      file_size BIGINT NOT NULL,
      mime_type VARCHAR(100) NOT NULL,
      metadata JSONB DEFAULT '{}',
      status VARCHAR(50) DEFAULT 'ready' CHECK (status IN ('ready', 'processing', 'error')),
      visibility VARCHAR(50) DEFAULT 'private' CHECK (visibility IN ('private', 'public')),
      created_by UUID REFERENCES users(id),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create jobs table
  await query(`
    CREATE TABLE IF NOT EXISTS jobs (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
      type VARCHAR(50) NOT NULL CHECK (type IN ('timelapse', 'thumbnail', 'ftp-sync')),
      status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
      progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
      data JSONB DEFAULT '{}',
      result JSONB,
      error_message TEXT,
      created_by UUID REFERENCES users(id),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      started_at TIMESTAMP,
      completed_at TIMESTAMP
    )
  `);

  // Create ftp_sources table
  await query(`
    CREATE TABLE IF NOT EXISTS ftp_sources (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
      name VARCHAR(255) NOT NULL,
      host VARCHAR(255) NOT NULL,
      port INTEGER NOT NULL DEFAULT 21,
      username VARCHAR(255) NOT NULL,
      password VARCHAR(255) NOT NULL,
      path VARCHAR(500) DEFAULT '/',
      is_secure BOOLEAN DEFAULT false,
      is_active BOOLEAN DEFAULT true,
      last_sync TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
}

async function cleanupTestData() {
  // Delete in reverse order of dependencies
  await query('DELETE FROM jobs');
  await query('DELETE FROM assets');
  await query('DELETE FROM ftp_sources');
  await query('DELETE FROM users');
  await query('DELETE FROM tenants');
}

async function insertTestData() {
  // Insert test tenant
  const tenantResult = await query(`
    INSERT INTO tenants (id, name, display_name, description, is_active)
    VALUES ('550e8400-e29b-41d4-a716-************', 'test-tenant', 'Test Tenant', 'Test tenant for unit tests', true)
    RETURNING id
  `);

  const tenantId = tenantResult.rows[0].id;

  // Insert test admin user
  await query(`
    INSERT INTO users (id, tenant_id, email, name, role, is_active)
    VALUES ('550e8400-e29b-41d4-a716-446655440001', $1, '<EMAIL>', 'Test Admin', 'admin', true)
  `, [tenantId]);

  // Insert test client user
  await query(`
    INSERT INTO users (id, tenant_id, email, name, role, is_active)
    VALUES ('550e8400-e29b-41d4-a716-446655440002', $1, '<EMAIL>', 'Test Client', 'client', true)
  `, [tenantId]);

  // Insert test assets
  await query(`
    INSERT INTO assets (id, tenant_id, name, type, file_path, file_size, mime_type, visibility, created_by)
    VALUES 
      ('550e8400-e29b-41d4-a716-446655440003', $1, 'test-image.jpg', 'image', '/test/image.jpg', 1024000, 'image/jpeg', 'public', '550e8400-e29b-41d4-a716-446655440001'),
      ('550e8400-e29b-41d4-a716-446655440004', $1, 'test-video.mp4', 'video', '/test/video.mp4', 5120000, 'video/mp4', 'private', '550e8400-e29b-41d4-a716-446655440001')
  `, [tenantId]);
}

// Test utilities
export const testData = {
  tenantId: '550e8400-e29b-41d4-a716-************',
  adminUserId: '550e8400-e29b-41d4-a716-446655440001',
  clientUserId: '550e8400-e29b-41d4-a716-446655440002',
  imageAssetId: '550e8400-e29b-41d4-a716-446655440003',
  videoAssetId: '550e8400-e29b-41d4-a716-446655440004',
};

export function createMockAuthRequest(userId: string, role: 'admin' | 'client') {
  return {
    user: {
      id: userId,
      tenantId: testData.tenantId,
      role,
      email: role === 'admin' ? '<EMAIL>' : '<EMAIL>',
      name: role === 'admin' ? 'Test Admin' : 'Test Client',
    }
  };
}
