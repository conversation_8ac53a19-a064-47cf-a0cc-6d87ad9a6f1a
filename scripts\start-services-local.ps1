# EagleView MVP - Start Local Services Script
# This script starts all required services locally

Write-Host "🚀 Starting EagleView MVP Local Services" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Function to check if a port is in use
function Test-Port($port) {
    $connection = Test-NetConnection -ComputerName localhost -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
    return $connection
}

# Function to start a service in a new window
function Start-ServiceWindow($title, $command) {
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "& { Write-Host '$title' -ForegroundColor Green; $command }" -WindowStyle Normal
}

Write-Host ""
Write-Host "📋 Checking if services are already running..." -ForegroundColor Yellow

# Check PostgreSQL (port 5432)
if (Test-Port 5432) {
    Write-Host "✅ PostgreSQL is already running on port 5432" -ForegroundColor Green
} else {
    Write-Host "🔄 Starting PostgreSQL..." -ForegroundColor Yellow
    Write-Host "⚠️  Please start PostgreSQL manually if it's not running" -ForegroundColor Yellow
    Write-Host "   - Windows Service: net start postgresql-x64-15" -ForegroundColor Cyan
    Write-Host "   - Or use pgAdmin to start the service" -ForegroundColor Cyan
}

# Check Redis (port 6379)
if (Test-Port 6379) {
    Write-Host "✅ Redis is already running on port 6379" -ForegroundColor Green
} else {
    Write-Host "🔄 Starting Redis..." -ForegroundColor Yellow
    if (Get-Command "redis-server" -ErrorAction SilentlyContinue) {
        Start-ServiceWindow "Redis Server" "redis-server"
        Start-Sleep 2
    } else {
        Write-Host "⚠️  Please start Redis manually:" -ForegroundColor Yellow
        Write-Host "   - Run: redis-server" -ForegroundColor Cyan
        Write-Host "   - Or start Redis service if installed as Windows service" -ForegroundColor Cyan
    }
}

# Check MinIO (port 9000)
if (Test-Port 9000) {
    Write-Host "✅ MinIO is already running on port 9000" -ForegroundColor Green
} else {
    Write-Host "🔄 Starting MinIO..." -ForegroundColor Yellow
    if (Get-Command "minio" -ErrorAction SilentlyContinue) {
        # Create MinIO data directory
        if (!(Test-Path "minio_data")) {
            New-Item -ItemType Directory -Path "minio_data"
        }
        
        # Set MinIO credentials
        $env:MINIO_ROOT_USER = "eagleview"
        $env:MINIO_ROOT_PASSWORD = "eagleview_dev_password"
        
        Start-ServiceWindow "MinIO Server" "minio server minio_data --console-address ':9001'"
        Start-Sleep 3
    } else {
        Write-Host "⚠️  Please start MinIO manually:" -ForegroundColor Yellow
        Write-Host "   - Set environment variables:" -ForegroundColor Cyan
        Write-Host "     `$env:MINIO_ROOT_USER = 'eagleview'" -ForegroundColor Cyan
        Write-Host "     `$env:MINIO_ROOT_PASSWORD = 'eagleview_dev_password'" -ForegroundColor Cyan
        Write-Host "   - Run: minio server minio_data --console-address ':9001'" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep 5

# Verify services are running
Write-Host ""
Write-Host "🔍 Verifying services..." -ForegroundColor Yellow

if (Test-Port 5432) {
    Write-Host "✅ PostgreSQL is ready on port 5432" -ForegroundColor Green
} else {
    Write-Host "❌ PostgreSQL is not running on port 5432" -ForegroundColor Red
}

if (Test-Port 6379) {
    Write-Host "✅ Redis is ready on port 6379" -ForegroundColor Green
} else {
    Write-Host "❌ Redis is not running on port 6379" -ForegroundColor Red
}

if (Test-Port 9000) {
    Write-Host "✅ MinIO is ready on port 9000" -ForegroundColor Green
} else {
    Write-Host "❌ MinIO is not running on port 9000" -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 Service URLs:" -ForegroundColor Yellow
Write-Host "  - PostgreSQL: localhost:5432" -ForegroundColor Cyan
Write-Host "  - Redis: localhost:6379" -ForegroundColor Cyan
Write-Host "  - MinIO API: http://localhost:9000" -ForegroundColor Cyan
Write-Host "  - MinIO Console: http://localhost:9001" -ForegroundColor Cyan
Write-Host "    (Login: eagleview / eagleview_dev_password)" -ForegroundColor Gray

Write-Host ""
Write-Host "🎉 Services startup completed!" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green
