{"name": "@eagleview/api", "version": "1.0.0", "description": "EagleView API - Backend service", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:migrate": "tsx src/database/migrate.ts", "db:seed": "tsx src/database/seed.ts", "test": "jest", "lint": "eslint src --ext .ts", "clean": "rm -rf dist"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "redis": "^4.6.10", "bullmq": "^4.15.0", "jsonwebtoken": "^9.0.2", "express-jwt": "^8.4.1", "jwks-rsa": "^3.1.0", "multer": "^1.4.5-lts.1", "fluent-ffmpeg": "^2.1.2", "basic-ftp": "^5.0.4", "aws-sdk": "^2.1498.0", "zod": "^3.22.4", "winston": "^3.11.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.9", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/fluent-ffmpeg": "^2.1.24", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "tsx": "^4.6.0", "typescript": "^5.3.0"}}