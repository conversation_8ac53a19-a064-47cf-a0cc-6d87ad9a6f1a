{"name": "@eagleview/api", "version": "1.0.0", "description": "EagleView API - Backend service", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:migrate": "tsx src/database/migrate.ts", "db:seed": "tsx src/database/seed.ts", "test": "jest", "lint": "eslint src --ext .ts", "clean": "rm -rf dist"}, "dependencies": {"@types/socket.io": "^3.0.2", "aws-sdk": "^2.1498.0", "basic-ftp": "^5.0.4", "bullmq": "^4.15.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-jwt": "^8.4.1", "fluent-ffmpeg": "^2.1.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "redis": "^4.6.10", "socket.io": "^4.8.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fluent-ffmpeg": "^2.1.24", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/pg": "^8.10.9", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}