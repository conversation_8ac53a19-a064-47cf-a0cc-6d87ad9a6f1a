import { useState } from 'react';
import { InformationCircleIcon } from '@heroicons/react/24/outline';

interface TimelapseSettings {
  fps: number;
  quality: 'high' | 'medium' | 'low';
  resolution: '4k' | '1080p' | '720p';
  outputName: string;
}

interface TimelapseSettingsProps {
  settings: TimelapseSettings;
  onSettingsChange: (settings: TimelapseSettings) => void;
  selectedCount: number;
}

export function TimelapseSettings({ settings, onSettingsChange, selectedCount }: TimelapseSettingsProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const updateSetting = (key: keyof TimelapseSettings, value: any) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  const calculateDuration = () => {
    return (selectedCount / settings.fps).toFixed(1);
  };

  const calculateFileSize = () => {
    // Rough estimation based on resolution and quality
    const resolutionMultiplier = {
      '4k': 4,
      '1080p': 1,
      '720p': 0.5
    };
    
    const qualityMultiplier = {
      'high': 1.5,
      'medium': 1,
      'low': 0.6
    };
    
    const baseSizeMB = 2; // Base size per second in MB for 1080p medium quality
    const duration = parseFloat(calculateDuration());
    
    const estimatedSize = baseSizeMB * duration * 
      resolutionMultiplier[settings.resolution] * 
      qualityMultiplier[settings.quality];
    
    return estimatedSize.toFixed(1);
  };

  const presetConfigs = [
    {
      name: 'Quick Preview',
      description: 'Fast processing, smaller file',
      settings: { fps: 15, quality: 'low' as const, resolution: '720p' as const }
    },
    {
      name: 'Standard Quality',
      description: 'Balanced quality and file size',
      settings: { fps: 30, quality: 'medium' as const, resolution: '1080p' as const }
    },
    {
      name: 'High Quality',
      description: 'Best quality, larger file',
      settings: { fps: 30, quality: 'high' as const, resolution: '4k' as const }
    },
    {
      name: 'Smooth Motion',
      description: 'Higher frame rate for smooth playback',
      settings: { fps: 60, quality: 'medium' as const, resolution: '1080p' as const }
    }
  ];

  const applyPreset = (presetSettings: Partial<TimelapseSettings>) => {
    onSettingsChange({
      ...settings,
      ...presetSettings
    });
  };

  return (
    <div className="space-y-6">
      {/* Output Name */}
      <div>
        <label htmlFor="outputName" className="block text-sm font-medium text-gray-700 mb-2">
          Timelapse Name
        </label>
        <input
          type="text"
          id="outputName"
          value={settings.outputName}
          onChange={(e) => updateSetting('outputName', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder="Enter timelapse name"
        />
      </div>

      {/* Presets */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-3">Quick Presets</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {presetConfigs.map((preset) => (
            <button
              key={preset.name}
              onClick={() => applyPreset(preset.settings)}
              className="text-left p-3 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div className="font-medium text-gray-900">{preset.name}</div>
              <div className="text-xs text-gray-500 mt-1">{preset.description}</div>
              <div className="text-xs text-gray-400 mt-1">
                {preset.settings.fps}fps • {preset.settings.quality} • {preset.settings.resolution}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Custom Settings */}
      <div>
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center text-sm font-medium text-gray-700 mb-3"
        >
          Custom Settings
          <svg
            className={`ml-2 h-4 w-4 transform transition-transform ${showAdvanced ? 'rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {showAdvanced && (
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            {/* Frame Rate */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Frame Rate (FPS)
                <div className="inline-flex items-center ml-1">
                  <InformationCircleIcon className="h-4 w-4 text-gray-400" title="Frames per second - higher values create smoother motion" />
                </div>
              </label>
              <div className="flex items-center space-x-4">
                <input
                  type="range"
                  min="10"
                  max="60"
                  step="5"
                  value={settings.fps}
                  onChange={(e) => updateSetting('fps', parseInt(e.target.value))}
                  className="flex-1"
                />
                <span className="text-sm font-medium text-gray-900 w-12">
                  {settings.fps}
                </span>
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Slower</span>
                <span>Faster</span>
              </div>
            </div>

            {/* Quality */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quality
                <div className="inline-flex items-center ml-1">
                  <InformationCircleIcon className="h-4 w-4 text-gray-400" title="Video compression quality - affects file size and visual quality" />
                </div>
              </label>
              <div className="grid grid-cols-3 gap-2">
                {(['low', 'medium', 'high'] as const).map((quality) => (
                  <button
                    key={quality}
                    onClick={() => updateSetting('quality', quality)}
                    className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      settings.quality === quality
                        ? 'bg-primary-600 text-white'
                        : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {quality.charAt(0).toUpperCase() + quality.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Resolution */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Resolution
                <div className="inline-flex items-center ml-1">
                  <InformationCircleIcon className="h-4 w-4 text-gray-400" title="Video resolution - higher resolutions create larger files" />
                </div>
              </label>
              <div className="grid grid-cols-3 gap-2">
                {(['720p', '1080p', '4k'] as const).map((resolution) => (
                  <button
                    key={resolution}
                    onClick={() => updateSetting('resolution', resolution)}
                    className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      settings.resolution === resolution
                        ? 'bg-primary-600 text-white'
                        : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {resolution}
                  </button>
                ))}
              </div>
              <div className="grid grid-cols-3 gap-2 mt-1 text-xs text-gray-500">
                <span>1280×720</span>
                <span>1920×1080</span>
                <span>3840×2160</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Preview Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">Timelapse Preview</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-blue-700">Frames:</span>
            <span className="ml-2 font-medium text-blue-900">{selectedCount}</span>
          </div>
          <div>
            <span className="text-blue-700">Duration:</span>
            <span className="ml-2 font-medium text-blue-900">{calculateDuration()}s</span>
          </div>
          <div>
            <span className="text-blue-700">Frame Rate:</span>
            <span className="ml-2 font-medium text-blue-900">{settings.fps} fps</span>
          </div>
          <div>
            <span className="text-blue-700">Est. Size:</span>
            <span className="ml-2 font-medium text-blue-900">~{calculateFileSize()} MB</span>
          </div>
        </div>
        
        {selectedCount < 2 && (
          <div className="mt-3 text-xs text-blue-700">
            ⚠️ Select at least 2 frames to create a timelapse
          </div>
        )}
        
        {parseFloat(calculateDuration()) < 1 && selectedCount >= 2 && (
          <div className="mt-3 text-xs text-blue-700">
            💡 Consider reducing frame rate for longer duration
          </div>
        )}
        
        {parseFloat(calculateFileSize()) > 100 && (
          <div className="mt-3 text-xs text-blue-700">
            💡 Large file size - consider reducing quality or resolution
          </div>
        )}
      </div>

      {/* Processing Time Estimate */}
      <div className="text-xs text-gray-500">
        <div className="flex items-center">
          <InformationCircleIcon className="h-4 w-4 mr-1" />
          Estimated processing time: {Math.ceil(selectedCount / 10)} - {Math.ceil(selectedCount / 5)} minutes
        </div>
      </div>
    </div>
  );
}
