import { useAuth0 } from '@auth0/auth0-react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useEffect } from 'react'
import { useAuthStore } from './stores/authStore'
import { useToastStore } from './stores/toastStore'
import { LoadingSpinner } from './components/ui/LoadingSpinner'
import { ToastContainer } from './components/ui/Toast'
import { Layout } from './components/layout/Layout'
import { LoginPage } from './pages/LoginPage'
import { DashboardPage } from './pages/DashboardPage'
import { AssetsPage } from './pages/AssetsPage'
import { JobsPage } from './pages/JobsPage'
import { UsersPage } from './pages/UsersPage'
import { TenantsPage } from './pages/TenantsPage'
import { FtpSourcesPage } from './pages/FtpSourcesPage'

function App() {
  const { isLoading, isAuthenticated, getAccessTokenSilently, user } = useAuth0()
  const { setToken, setUser, user: storeUser } = useAuthStore()
  const { toasts, dismissToast, success, error } = useToastStore()

  useEffect(() => {
    const initializeAuth = async () => {
      if (isAuthenticated && user) {
        try {
          const token = await getAccessTokenSilently()
          setToken(token)
          
          // Call backend to sync user data
          const response = await fetch('/api/auth/callback', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify({
              auth0Id: user.sub,
              email: user.email,
              name: user.name,
            }),
          })

          if (response.ok) {
            const userData = await response.json()
            setUser(userData.user)
            success('Welcome!', `Logged in as ${userData.user.name}`)
          } else {
            const errorData = await response.json()
            error('Authentication failed', errorData.error)
          }
        } catch (error) {
          console.error('Failed to initialize auth:', error)
          error('Connection failed', 'Unable to connect to the server')
        }
      }
    }

    initializeAuth()
  }, [isAuthenticated, user, getAccessTokenSilently, setToken, setUser])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!isAuthenticated) {
    return <LoginPage />
  }

  if (!storeUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<DashboardPage />} />
          <Route path="/assets" element={<AssetsPage />} />
          <Route path="/jobs" element={<JobsPage />} />
          {storeUser.role === 'admin' && (
            <>
              <Route path="/users" element={<UsersPage />} />
              <Route path="/tenants" element={<TenantsPage />} />
              <Route path="/ftp-sources" element={<FtpSourcesPage />} />
            </>
          )}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Layout>

      {/* Global Toast Notifications */}
      <ToastContainer toasts={toasts} onDismiss={dismissToast} />
    </>
  )
}

export default App
