# EagleView MVP - Complete Local Startup Script
# This script starts all services and the application locally (without <PERSON><PERSON>)

param(
    [switch]$SkipServices,
    [switch]$SkipDatabase,
    [switch]$Help
)

if ($Help) {
    Write-Host "EagleView MVP - Local Startup Script" -ForegroundColor Green
    Write-Host "====================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\scripts\start-local.ps1 [options]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -SkipServices    Skip starting infrastructure services" -ForegroundColor Cyan
    Write-Host "  -SkipDatabase    Skip database setup" -ForegroundColor Cyan
    Write-Host "  -Help           Show this help message" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\scripts\start-local.ps1                    # Full startup" -ForegroundColor Cyan
    Write-Host "  .\scripts\start-local.ps1 -SkipServices     # Skip services, start app only" -ForegroundColor Cyan
    Write-Host "  .\scripts\start-local.ps1 -SkipDatabase     # Skip database setup" -ForegroundColor Cyan
    exit 0
}

Write-Host "🚀 Starting EagleView MVP Locally (Without Docker)" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Green

# Step 1: Setup environment if needed
Write-Host ""
Write-Host "📋 Step 1: Environment Setup" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

if (!(Test-Path "api\.env") -or !(Test-Path "frontend\.env")) {
    Write-Host "⚙️  Setting up environment files..." -ForegroundColor Yellow
    & ".\scripts\setup-local-windows.ps1"
} else {
    Write-Host "✅ Environment files already exist" -ForegroundColor Green
}

# Step 2: Start infrastructure services
if (!$SkipServices) {
    Write-Host ""
    Write-Host "📋 Step 2: Infrastructure Services" -ForegroundColor Yellow
    Write-Host "===================================" -ForegroundColor Yellow
    
    & ".\scripts\start-services-local.ps1"
    
    Write-Host ""
    Write-Host "⏳ Waiting for services to stabilize..." -ForegroundColor Yellow
    Start-Sleep 5
} else {
    Write-Host ""
    Write-Host "⏭️  Skipping infrastructure services startup" -ForegroundColor Yellow
}

# Step 3: Setup database
if (!$SkipDatabase) {
    Write-Host ""
    Write-Host "📋 Step 3: Database Setup" -ForegroundColor Yellow
    Write-Host "==========================" -ForegroundColor Yellow
    
    $dbSetup = Read-Host "Do you want to setup the database? (Y/n)"
    if ($dbSetup -ne "n" -and $dbSetup -ne "N") {
        & ".\scripts\setup-database-local.ps1"
        
        Write-Host ""
        Write-Host "🔄 Running database migrations..." -ForegroundColor Yellow
        try {
            pnpm db:migrate
            Write-Host "✅ Database migrations completed" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  Database migration failed. You may need to run it manually later." -ForegroundColor Yellow
        }
    }
} else {
    Write-Host ""
    Write-Host "⏭️  Skipping database setup" -ForegroundColor Yellow
}

# Step 4: Start application services
Write-Host ""
Write-Host "📋 Step 4: Application Services" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

Write-Host ""
Write-Host "🔧 Building application..." -ForegroundColor Yellow
try {
    pnpm build
    Write-Host "✅ Build completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Build failed, but continuing with development mode..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 Starting development servers..." -ForegroundColor Green

# Create a new PowerShell window for the development servers
$devCommand = @"
Write-Host '🚀 EagleView MVP Development Servers' -ForegroundColor Green
Write-Host '====================================' -ForegroundColor Green
Write-Host ''
Write-Host 'Starting all services...' -ForegroundColor Yellow
Write-Host ''

# Change to the project directory
Set-Location '$PWD'

# Start the development servers
pnpm dev
"@

Start-Process powershell -ArgumentList "-NoExit", "-Command", "& { $devCommand }" -WindowStyle Normal

Write-Host ""
Write-Host "🎉 EagleView MVP is starting!" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Application URLs:" -ForegroundColor Yellow
Write-Host "  - Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "  - API: http://localhost:3001" -ForegroundColor Cyan
Write-Host "  - API Health: http://localhost:3001/health" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Infrastructure URLs:" -ForegroundColor Yellow
Write-Host "  - MinIO Console: http://localhost:9001" -ForegroundColor Cyan
Write-Host "    (Login: eagleview / eagleview_dev_password)" -ForegroundColor Gray
Write-Host "  - PostgreSQL: localhost:5432" -ForegroundColor Cyan
Write-Host "  - Redis: localhost:6379" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Configure Auth0 credentials in:" -ForegroundColor Cyan
Write-Host "   - api\.env" -ForegroundColor Gray
Write-Host "   - frontend\.env" -ForegroundColor Gray
Write-Host "2. Wait for all services to start (check the new PowerShell window)" -ForegroundColor Cyan
Write-Host "3. Open http://localhost:3000 in your browser" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Useful Commands:" -ForegroundColor Yellow
Write-Host "  - Stop all: Ctrl+C in the development servers window" -ForegroundColor Cyan
Write-Host "  - View logs: Check the development servers window" -ForegroundColor Cyan
Write-Host "  - Restart: Close the dev window and run this script again" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔧 Troubleshooting:" -ForegroundColor Yellow
Write-Host "  - If services fail to start, check if ports are available" -ForegroundColor Cyan
Write-Host "  - If database connection fails, verify PostgreSQL is running" -ForegroundColor Cyan
Write-Host "  - If Redis connection fails, verify Redis is running" -ForegroundColor Cyan
Write-Host "  - Check the development servers window for detailed error messages" -ForegroundColor Cyan
