import { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { useToastStore } from '../stores/toastStore';
import { FtpSourceForm } from '../components/ftp/FtpSourceForm';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { 
  PlusIcon, 
  ServerIcon, 
  PlayIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface FtpSource {
  id: string;
  name: string;
  host: string;
  port: number;
  username: string;
  path: string;
  is_secure: boolean;
  is_active: boolean;
  last_sync?: string;
  created_at: string;
}

export function FtpSourcesPage() {
  const { token, user } = useAuthStore();
  const { success, error } = useToastStore();
  const [ftpSources, setFtpSources] = useState<FtpSource[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingSource, setEditingSource] = useState<FtpSource | undefined>();

  useEffect(() => {
    fetchFtpSources();
  }, []);

  const fetchFtpSources = async () => {
    try {
      const response = await fetch('/api/ftp-sources', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setFtpSources(data.ftpSources);
      }
    } catch (err) {
      console.error('Failed to fetch FTP sources:', err);
      error('Failed to Load', 'Could not load FTP sources');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = (ftpSource: FtpSource) => {
    if (editingSource) {
      setFtpSources(prev => prev.map(source => 
        source.id === ftpSource.id ? ftpSource : source
      ));
    } else {
      setFtpSources(prev => [ftpSource, ...prev]);
    }
    setEditingSource(undefined);
  };

  const handleEdit = (ftpSource: FtpSource) => {
    setEditingSource(ftpSource);
    setShowForm(true);
  };

  const handleDelete = async (ftpSource: FtpSource) => {
    if (!confirm(`Are you sure you want to delete "${ftpSource.name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/ftp-sources/${ftpSource.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setFtpSources(prev => prev.filter(source => source.id !== ftpSource.id));
        success('FTP Source Deleted', `Successfully deleted "${ftpSource.name}"`);
      } else {
        const errorData = await response.json();
        error('Delete Failed', errorData.error);
      }
    } catch (err) {
      console.error('Failed to delete FTP source:', err);
      error('Delete Error', 'Failed to delete FTP source');
    }
  };

  const handleSync = async (ftpSource: FtpSource) => {
    try {
      const response = await fetch(`/api/ftp-sources/${ftpSource.id}/sync`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        success(
          'Sync Started', 
          `Manual sync initiated for "${ftpSource.name}". Check Jobs page for progress.`,
          {
            action: {
              label: 'View Jobs',
              onClick: () => window.location.href = '/jobs'
            }
          }
        );
      } else {
        const errorData = await response.json();
        error('Sync Failed', errorData.error);
      }
    } catch (err) {
      console.error('Failed to start sync:', err);
      error('Sync Error', 'Failed to start manual sync');
    }
  };

  const handleTest = async (ftpSource: FtpSource) => {
    try {
      const response = await fetch(`/api/ftp-sources/${ftpSource.id}/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        success(
          'Connection Test Started', 
          `Testing connection to "${ftpSource.name}". Check Jobs page for results.`,
          {
            action: {
              label: 'View Jobs',
              onClick: () => window.location.href = '/jobs'
            }
          }
        );
      } else {
        const errorData = await response.json();
        error('Test Failed', errorData.error);
      }
    } catch (err) {
      console.error('Failed to test connection:', err);
      error('Test Error', 'Failed to test FTP connection');
    }
  };

  const formatLastSync = (lastSync?: string) => {
    if (!lastSync) return 'Never';
    
    const date = new Date(lastSync);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  if (user?.role !== 'admin') {
    return (
      <div className="text-center py-12">
        <XCircleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-semibold text-gray-900">Access Denied</h3>
        <p className="mt-1 text-sm text-gray-500">
          You need admin privileges to manage FTP sources.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            FTP Sources
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage camera FTP connections for automatic image synchronization
          </p>
        </div>
        <div className="mt-4 flex md:ml-4 md:mt-0">
          <button
            onClick={() => {
              setEditingSource(undefined);
              setShowForm(true);
            }}
            type="button"
            className="inline-flex items-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add FTP Source
          </button>
        </div>
      </div>

      <div className="mt-8">
        {loading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : ftpSources.length === 0 ? (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="text-center">
                <ServerIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">No FTP sources</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by adding your first camera FTP connection.
                </p>
                <div className="mt-6">
                  <button
                    onClick={() => {
                      setEditingSource(undefined);
                      setShowForm(true);
                    }}
                    type="button"
                    className="inline-flex items-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add FTP Source
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {ftpSources.map((ftpSource) => (
              <div
                key={ftpSource.id}
                className="bg-white overflow-hidden shadow rounded-lg"
              >
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <ServerIcon className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="ml-4 flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {ftpSource.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {ftpSource.host}:{ftpSource.port}
                      </p>
                    </div>
                    <div className="flex-shrink-0">
                      {ftpSource.is_active ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  </div>

                  <div className="mt-4">
                    <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                      <div>
                        <dt className="text-gray-500">Username:</dt>
                        <dd className="text-gray-900">{ftpSource.username}</dd>
                      </div>
                      <div>
                        <dt className="text-gray-500">Path:</dt>
                        <dd className="text-gray-900">{ftpSource.path}</dd>
                      </div>
                      <div>
                        <dt className="text-gray-500">Security:</dt>
                        <dd className="text-gray-900">
                          {ftpSource.is_secure ? 'FTPS/SFTP' : 'FTP'}
                        </dd>
                      </div>
                      <div>
                        <dt className="text-gray-500">Last Sync:</dt>
                        <dd className="text-gray-900">
                          {formatLastSync(ftpSource.last_sync)}
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <div className="mt-6 flex justify-between">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleSync(ftpSource)}
                        className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        <PlayIcon className="h-3 w-3 mr-1" />
                        Sync
                      </button>
                      <button
                        onClick={() => handleTest(ftpSource)}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Test
                      </button>
                    </div>
                    
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(ftpSource)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(ftpSource)}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Form Modal */}
      {showForm && (
        <FtpSourceForm
          ftpSource={editingSource}
          onClose={() => {
            setShowForm(false);
            setEditingSource(undefined);
          }}
          onSave={handleSave}
        />
      )}
    </div>
  );
}
