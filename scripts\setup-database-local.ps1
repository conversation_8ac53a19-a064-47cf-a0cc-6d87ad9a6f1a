# EagleView MVP - Database Setup Script for Local PostgreSQL
# This script creates the database and user for local development

Write-Host "🗄️  Setting up EagleView Database Locally" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Database configuration
$DB_NAME = "eagleview"
$DB_USER = "eagleview"
$DB_PASSWORD = "eagleview_dev_password"

Write-Host ""
Write-Host "📋 Database Configuration:" -ForegroundColor Yellow
Write-Host "  - Database: $DB_NAME" -ForegroundColor Cyan
Write-Host "  - User: $DB_USER" -ForegroundColor Cyan
Write-Host "  - Password: $DB_PASSWORD" -ForegroundColor Cyan

Write-Host ""
Write-Host "🔍 Checking PostgreSQL connection..." -ForegroundColor Yellow

# Test if PostgreSQL is running
try {
    $result = psql -U postgres -c "SELECT version();" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ PostgreSQL is running" -ForegroundColor Green
    } else {
        throw "PostgreSQL connection failed"
    }
} catch {
    Write-Host "❌ Cannot connect to PostgreSQL" -ForegroundColor Red
    Write-Host "Please ensure PostgreSQL is running and you can connect as 'postgres' user" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To start PostgreSQL:" -ForegroundColor Yellow
    Write-Host "  - Windows Service: net start postgresql-x64-15" -ForegroundColor Cyan
    Write-Host "  - Or use pgAdmin to start the service" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "If you need to set up postgres user password:" -ForegroundColor Yellow
    Write-Host "  - Run: psql -U postgres" -ForegroundColor Cyan
    Write-Host "  - Then: ALTER USER postgres PASSWORD 'your_password';" -ForegroundColor Cyan
    exit 1
}

Write-Host ""
Write-Host "👤 Creating database user..." -ForegroundColor Yellow

# Create user if not exists
$createUserSQL = @"
DO `$`$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '$DB_USER') THEN
        CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';
        GRANT CREATEDB TO $DB_USER;
        ALTER USER $DB_USER CREATEDB;
    END IF;
END
`$`$;
"@

try {
    $createUserSQL | psql -U postgres
    Write-Host "✅ User '$DB_USER' created/verified" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create user" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🗄️  Creating database..." -ForegroundColor Yellow

# Create database if not exists
$createDbSQL = @"
SELECT 'CREATE DATABASE $DB_NAME OWNER $DB_USER'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DB_NAME')\gexec
"@

try {
    $createDbSQL | psql -U postgres
    Write-Host "✅ Database '$DB_NAME' created/verified" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create database" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🔧 Setting up database schema..." -ForegroundColor Yellow

# Check if init.sql exists
if (Test-Path "api\src\database\init.sql") {
    try {
        psql -U $DB_USER -d $DB_NAME -f "api\src\database\init.sql"
        Write-Host "✅ Database schema initialized" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to initialize schema" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
        Write-Host "You may need to run the migration manually later" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  init.sql not found, skipping schema initialization" -ForegroundColor Yellow
    Write-Host "You'll need to run migrations manually: pnpm db:migrate" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔍 Verifying database setup..." -ForegroundColor Yellow

try {
    $tableCount = psql -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | ForEach-Object { $_.Trim() }
    Write-Host "✅ Database has $tableCount tables" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not verify table count" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📋 Database Connection String:" -ForegroundColor Yellow
Write-Host "  postgresql://$DB_USER`:$DB_PASSWORD@localhost:5432/$DB_NAME" -ForegroundColor Cyan

Write-Host ""
Write-Host "🎉 Database setup completed!" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Verify the DATABASE_URL in api\.env matches the connection string above" -ForegroundColor Cyan
Write-Host "2. Run database migrations: pnpm db:migrate" -ForegroundColor Cyan
Write-Host "3. (Optional) Seed the database: pnpm db:seed" -ForegroundColor Cyan
