import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useAuthStore } from '../../stores/authStore';
import { useToastStore } from '../../stores/toastStore';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { XMarkIcon, PhotoIcon } from '@heroicons/react/24/outline';

interface AssetUploadProps {
  onUploadComplete?: (assets: any[]) => void;
  onClose?: () => void;
}

interface UploadFile {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  assetId?: string;
}

export function AssetUpload({ onUploadComplete, onClose }: AssetUploadProps) {
  const { token } = useAuthStore();
  const { success, error } = useToastStore();
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      file,
      id: Math.random().toString(36).substring(7),
      progress: 0,
      status: 'pending'
    }));

    setUploadFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png'],
      'video/*': ['.mp4', '.mov', '.avi']
    },
    maxSize: 100 * 1024 * 1024, // 100MB
  });

  const removeFile = (id: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== id));
  };

  const handleUploadFiles = async () => {
    if (uploadFiles.length === 0) return;

    setIsUploading(true);
    const completedAssets: any[] = [];

    for (const uploadFile of uploadFiles) {
      if (uploadFile.status !== 'pending') continue;

      try {
        // Update status to uploading
        setUploadFiles(prev => 
          prev.map(f => f.id === uploadFile.id ? { ...f, status: 'uploading' as const } : f)
        );

        const formData = new FormData();
        formData.append('file', uploadFile.file);
        formData.append('name', uploadFile.file.name);
        formData.append('type', uploadFile.file.type.startsWith('image/') ? 'image' : 'video');
        formData.append('visibility', 'private');

        const response = await fetch('/api/assets', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Upload failed: ${response.statusText}`);
        }

        const result = await response.json();
        completedAssets.push(result.asset);

        // Update status to completed
        setUploadFiles(prev => 
          prev.map(f => f.id === uploadFile.id ? { 
            ...f, 
            status: 'completed' as const, 
            progress: 100,
            assetId: result.asset.id 
          } : f)
        );

        // Schedule thumbnail generation for images
        if (uploadFile.file.type.startsWith('image/')) {
          try {
            await fetch('/api/jobs/thumbnail', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                assetId: result.asset.id,
              }),
            });
          } catch (thumbnailError) {
            console.warn('Failed to schedule thumbnail generation:', thumbnailError);
          }
        }

      } catch (error) {
        console.error('Upload error:', error);
        
        // Update status to error
        setUploadFiles(prev => 
          prev.map(f => f.id === uploadFile.id ? { 
            ...f, 
            status: 'error' as const,
            error: error instanceof Error ? error.message : 'Upload failed'
          } : f)
        );
      }
    }

    setIsUploading(false);

    if (completedAssets.length > 0) {
      success(
        'Upload Complete!',
        `Successfully uploaded ${completedAssets.length} file${completedAssets.length !== 1 ? 's' : ''}`
      );

      if (onUploadComplete) {
        onUploadComplete(completedAssets);
      }
    } else {
      error('Upload Failed', 'No files were uploaded successfully');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending': return 'text-gray-500';
      case 'uploading': return 'text-blue-500';
      case 'completed': return 'text-green-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusText = (file: UploadFile) => {
    switch (file.status) {
      case 'pending': return 'Ready to upload';
      case 'uploading': return 'Uploading...';
      case 'completed': return 'Upload complete';
      case 'error': return file.error || 'Upload failed';
      default: return '';
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Upload Assets</h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          )}
        </div>

        {/* Dropzone */}
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragActive
              ? 'border-primary-500 bg-primary-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <input {...getInputProps()} />
          <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">
            {isDragActive
              ? 'Drop the files here...'
              : 'Drag & drop files here, or click to select files'}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Supports: JPEG, PNG, MP4, MOV, AVI (max 100MB)
          </p>
        </div>

        {/* File list */}
        {uploadFiles.length > 0 && (
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Files to upload</h4>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {uploadFiles.map((uploadFile) => (
                <div
                  key={uploadFile.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {uploadFile.file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(uploadFile.file.size)}
                    </p>
                    <p className={`text-xs ${getStatusColor(uploadFile.status)}`}>
                      {getStatusText(uploadFile)}
                    </p>
                  </div>
                  
                  {uploadFile.status === 'uploading' && (
                    <LoadingSpinner size="sm" className="ml-3" />
                  )}
                  
                  {uploadFile.status === 'pending' && (
                    <button
                      onClick={() => removeFile(uploadFile.id)}
                      className="ml-3 text-gray-400 hover:text-red-500"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 mt-6">
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={isUploading}
            >
              Cancel
            </button>
          )}
          <button
            onClick={handleUploadFiles}
            disabled={uploadFiles.length === 0 || isUploading}
            className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isUploading && <LoadingSpinner size="sm" className="mr-2" />}
            {isUploading ? 'Uploading...' : `Upload ${uploadFiles.length} file${uploadFiles.length !== 1 ? 's' : ''}`}
          </button>
        </div>
      </div>
    </div>
  );
}
