import dotenv from 'dotenv';
import { Worker } from 'bullmq';
import { createClient } from 'redis';
import { logger } from './utils/logger';
import { connectDatabase } from './database/connection';
import { processTimelapseJob } from './processors/timelapseProcessor';
import { processThumbnailJob } from './processors/thumbnailProcessor';
import { processFtpSyncJob } from './processors/ftpSyncProcessor';

dotenv.config();

const redisConnection = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
};

async function startWorker() {
  try {
    // Connect to database
    await connectDatabase();
    logger.info('Database connected successfully');

    // Create workers for different job types
    const timelapseWorker = new Worker('timelapse', processTimelapseJob, {
      connection: redisConnection,
      concurrency: 2, // Process 2 timelapse jobs concurrently
    });

    const thumbnailWorker = new Worker('thumbnail', processThumbnailJob, {
      connection: redisConnection,
      concurrency: 5, // Process 5 thumbnail jobs concurrently
    });

    const ftpSyncWorker = new Worker('ftp-sync', processFtpSyncJob, {
      connection: redisConnection,
      concurrency: 1, // Process 1 FTP sync job at a time
    });

    // Worker event handlers
    timelapseWorker.on('completed', (job) => {
      logger.info(`Timelapse job completed: ${job.id}`);
    });

    timelapseWorker.on('failed', (job, err) => {
      logger.error(`Timelapse job failed: ${job?.id}`, err);
    });

    thumbnailWorker.on('completed', (job) => {
      logger.info(`Thumbnail job completed: ${job.id}`);
    });

    thumbnailWorker.on('failed', (job, err) => {
      logger.error(`Thumbnail job failed: ${job?.id}`, err);
    });

    ftpSyncWorker.on('completed', (job) => {
      logger.info(`FTP sync job completed: ${job.id}`);
    });

    ftpSyncWorker.on('failed', (job, err) => {
      logger.error(`FTP sync job failed: ${job?.id}`, err);
    });

    logger.info('Workers started successfully');

    // Graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('Shutting down workers...');
      await Promise.all([
        timelapseWorker.close(),
        thumbnailWorker.close(),
        ftpSyncWorker.close(),
      ]);
      process.exit(0);
    });

  } catch (error) {
    logger.error('Failed to start worker:', error);
    process.exit(1);
  }
}

startWorker();
