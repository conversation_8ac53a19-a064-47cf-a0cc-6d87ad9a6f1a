import { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { useToastStore } from '../stores/toastStore';
import { UserForm } from '../components/users/UserForm';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import {
  PlusIcon,
  UsersIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  ShieldCheckIcon,
  UserIcon
} from '@heroicons/react/24/outline';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'client';
  tenant_id: string;
  is_active: boolean;
  created_at: string;
  tenant_name?: string;
}

export function UsersPage() {
  const { token, user: currentUser } = useAuthStore();
  const { success, error } = useToastStore();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | undefined>();
  const [filter, setFilter] = useState<'all' | 'admin' | 'client' | 'active' | 'inactive'>('all');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      }
    } catch (err) {
      console.error('Failed to fetch users:', err);
      error('Failed to Load', 'Could not load users');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = (user: User) => {
    if (editingUser) {
      setUsers(prev => prev.map(u => u.id === user.id ? user : u));
    } else {
      setUsers(prev => [user, ...prev]);
    }
    setEditingUser(undefined);
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setShowForm(true);
  };

  const handleDelete = async (user: User) => {
    if (!confirm(`Are you sure you want to delete "${user.name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setUsers(prev => prev.filter(u => u.id !== user.id));
        success('User Deleted', `Successfully deleted "${user.name}"`);
      } else {
        const errorData = await response.json();
        error('Delete Failed', errorData.error);
      }
    } catch (err) {
      console.error('Failed to delete user:', err);
      error('Delete Error', 'Failed to delete user');
    }
  };

  const toggleUserStatus = async (user: User) => {
    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !user.is_active
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(prev => prev.map(u => u.id === user.id ? data.user : u));
        success(
          'User Updated',
          `${user.name} is now ${!user.is_active ? 'active' : 'inactive'}`
        );
      } else {
        const errorData = await response.json();
        error('Update Failed', errorData.error);
      }
    } catch (err) {
      console.error('Failed to update user:', err);
      error('Update Error', 'Failed to update user status');
    }
  };

  const filteredUsers = users.filter(user => {
    switch (filter) {
      case 'admin': return user.role === 'admin';
      case 'client': return user.role === 'client';
      case 'active': return user.is_active;
      case 'inactive': return !user.is_active;
      default: return true;
    }
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getRoleIcon = (role: string) => {
    return role === 'admin' ? (
      <ShieldCheckIcon className="h-4 w-4 text-purple-500" />
    ) : (
      <UserIcon className="h-4 w-4 text-blue-500" />
    );
  };

  if (currentUser?.role !== 'admin') {
    return (
      <div className="text-center py-12">
        <XCircleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-semibold text-gray-900">Access Denied</h3>
        <p className="mt-1 text-sm text-gray-500">
          You need admin privileges to manage users.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Users
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage user accounts and permissions
          </p>
        </div>
        <div className="mt-4 flex md:ml-4 md:mt-0">
          <button
            onClick={() => {
              setEditingUser(undefined);
              setShowForm(true);
            }}
            type="button"
            className="inline-flex items-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add User
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="mt-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'all', label: 'All Users' },
              { key: 'admin', label: 'Admins' },
              { key: 'client', label: 'Clients' },
              { key: 'active', label: 'Active' },
              { key: 'inactive', label: 'Inactive' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  filter === tab.key
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      <div className="mt-8">
        {loading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="text-center">
                <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">
                  {filter === 'all' ? 'No users' : `No ${filter} users`}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {filter === 'all'
                    ? 'Get started by adding users to your organization.'
                    : `No users match the ${filter} filter.`
                  }
                </p>
                {filter === 'all' && (
                  <div className="mt-6">
                    <button
                      onClick={() => {
                        setEditingUser(undefined);
                        setShowForm(true);
                      }}
                      type="button"
                      className="inline-flex items-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add User
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <li key={user.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {user.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="flex items-center">
                            <p className="text-sm font-medium text-gray-900">
                              {user.name}
                            </p>
                            <div className="ml-2 flex items-center">
                              {getRoleIcon(user.role)}
                              <span className="ml-1 text-xs text-gray-500 capitalize">
                                {user.role}
                              </span>
                            </div>
                          </div>
                          <p className="text-sm text-gray-500">{user.email}</p>
                          {user.tenant_name && (
                            <p className="text-xs text-gray-400">{user.tenant_name}</p>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          {user.is_active ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-500" />
                          ) : (
                            <XCircleIcon className="h-5 w-5 text-red-500" />
                          )}
                          <span className="ml-1 text-sm text-gray-500">
                            {user.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>

                        <span className="text-sm text-gray-500">
                          {formatDate(user.created_at)}
                        </span>

                        <div className="flex space-x-2">
                          <button
                            onClick={() => toggleUserStatus(user)}
                            className={`text-xs px-2 py-1 rounded ${
                              user.is_active
                                ? 'text-red-600 hover:text-red-700'
                                : 'text-green-600 hover:text-green-700'
                            }`}
                          >
                            {user.is_active ? 'Deactivate' : 'Activate'}
                          </button>

                          <button
                            onClick={() => handleEdit(user)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>

                          {user.id !== currentUser?.id && (
                            <button
                              onClick={() => handleDelete(user)}
                              className="text-gray-400 hover:text-red-600"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Form Modal */}
      {showForm && (
        <UserForm
          user={editingUser}
          onClose={() => {
            setShowForm(false);
            setEditingUser(undefined);
          }}
          onSave={handleSave}
        />
      )}
    </div>
  );
}
